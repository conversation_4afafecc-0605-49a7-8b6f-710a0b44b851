import React from "react";
import { View, Text, StyleSheet, TouchableOpacity, Image } from "react-native";
import { Team } from "../../../../types";

interface FavouriteTeamCardProps {
  team: Team;
  onPress?: () => void;
}

export default function FavouriteTeamCard({
  team,
  onPress,
}: FavouriteTeamCardProps) {
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.logoContainer}>
        {team.logo ? (
          <Image
            source={{ uri: team.logo }}
            style={styles.logo}
            resizeMode="contain"
          />
        ) : (
          <View style={styles.logoPlaceholder}>
            <Text style={styles.logoPlaceholderText}>
              {team.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
      </View>
      <Text style={styles.teamName} numberOfLines={1}>
        {team.name}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: 7,
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
    shadowOffset: { width: 0, height: 0 },
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 16,
    marginHorizontal: 10,
    marginBottom: 10,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  logoContainer: {
    width: 40,
    height: 40,
  },
  logo: {
    width: 40,
    height: 40,
  },
  logoPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#d32f2f",
    justifyContent: "center",
    alignItems: "center",
  },
  logoPlaceholderText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  teamName: {
    flex: 1,
    fontSize: 18,
    fontWeight: "500",
    color: "#231716",
  },
});
