import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { Player, Coach } from "../../../types";

// Mock data for the team
const mockTeamData = {
  id: "1",
  name: "Florida Panthers",
  logo: "",
  players: [
    {
      id: "1",
      name: "<PERSON>",
      position: "Center",
      jerseyNumber: 19,
      teamId: "1",
      avatar: "",
    },
    {
      id: "2",
      name: "<PERSON>",
      position: "<PERSON> Winger",
      jerseyNumber: 59,
      teamId: "1",
      avatar: "",
    },
    {
      id: "3",
      name: "<PERSON>",
      position: "Defender",
      jerseyNumber: 1,
      teamId: "1",
      avatar: "",
    },
    {
      id: "4",
      name: "<PERSON>",
      position: "<PERSON><PERSON>",
      jerseyNumber: 7,
      teamId: "1",
      avatar: "",
    },
  ] as Player[],
  coaches: [
    {
      id: "1",
      name: "<PERSON><PERSON>",
      position: "Head Coach",
      avatar: "",
    },
    {
      id: "2",
      name: "<PERSON> <PERSON>",
      position: "Assistant Coach",
      avatar: "",
    },
  ] as Coach[],
};

export default function TeamDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();

  const renderPlayerCard = (player: Player) => (
    <View key={player.id} style={styles.playerCard}>
      <View style={styles.avatarContainer}>
        {player.avatar ? (
          <Image
            source={{ uri: player.avatar }}
            style={styles.avatar}
            resizeMode="cover"
          />
        ) : (
          <View style={styles.avatarPlaceholder}>
            <Text style={styles.avatarPlaceholderText}>
              {player.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
      </View>
      <View style={styles.playerInfo}>
        <Text style={styles.playerName}>{player.name}</Text>
        <Text style={styles.playerPosition}>{player.position}</Text>
      </View>
      <Text style={styles.jerseyNumber}>
        {player.jerseyNumber.toString().padStart(2, "0")}
      </Text>
    </View>
  );

  const renderCoachCard = (coach: Coach) => (
    <View key={coach.id} style={styles.playerCard}>
      <View style={styles.avatarContainer}>
        {coach.avatar ? (
          <Image
            source={{ uri: coach.avatar }}
            style={styles.avatar}
            resizeMode="cover"
          />
        ) : (
          <View style={styles.avatarPlaceholder}>
            <Text style={styles.avatarPlaceholderText}>
              {coach.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
      </View>
      <View style={styles.playerInfo}>
        <Text style={styles.playerName}>{coach.name}</Text>
        <Text style={styles.playerPosition}>{coach.position}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Team Info Section */}
        <View style={styles.teamSection}>
          <View style={styles.teamHeader}>
            <View style={styles.teamLogoContainer}>
              {mockTeamData.logo ? (
                <Image
                  source={{ uri: mockTeamData.logo }}
                  style={styles.teamLogo}
                  resizeMode="contain"
                />
              ) : (
                <View style={styles.teamLogoPlaceholder}>
                  <Text style={styles.teamLogoPlaceholderText}>
                    {mockTeamData.name.charAt(0).toUpperCase()}
                  </Text>
                </View>
              )}
            </View>
            <Text style={styles.teamName}>{mockTeamData.name}</Text>
            <TouchableOpacity style={styles.heartButton}>
              <Ionicons name="heart" size={24} color="#d32f2f" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="calendar" size={18} color="#222" />
            <Text style={styles.actionButtonText}>Schedule</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="stats-chart" size={18} color="#222" />
            <Text style={styles.actionButtonText}>Statistics</Text>
          </TouchableOpacity>
        </View>

        {/* Team Roster Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Team roster</Text>
          <View style={styles.rosterContainer}>
            {mockTeamData.players.map(renderPlayerCard)}
          </View>
        </View>

        {/* Coaching Staff Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Coaching staff</Text>
          <View style={styles.rosterContainer}>
            {mockTeamData.coaches.map(renderCoachCard)}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f3f3f6",
  },
  scrollView: {
    flex: 1,
  },
  teamSection: {
    backgroundColor: "#fff",
  },
  teamHeader: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 10,
    gap: 8,
  },
  teamLogoContainer: {
    width: 36,
    height: 36,
  },
  teamLogo: {
    width: 36,
    height: 36,
  },
  teamLogoPlaceholder: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "#D52B1E",
    justifyContent: "center",
    alignItems: "center",
  },
  teamLogoPlaceholderText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  teamName: {
    fontSize: 26,
    fontWeight: "bold",
    color: "#222",
    flex: 1,
  },
  heartButton: {
    padding: 4,
  },
  actionButtons: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  actionButton: {
    flex: 1,
    backgroundColor: "#fff",
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 1,
    paddingHorizontal: 28,
    paddingVertical: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  actionButtonText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#222",
  },
  section: {
    paddingHorizontal: 16,
    paddingTop: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#231716",
    marginBottom: 8,
  },
  rosterContainer: {
    gap: 10,
  },
  playerCard: {
    backgroundColor: "#fff",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 1,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 10,
    gap: 12,
  },
  avatarContainer: {
    width: 38,
    height: 38,
  },
  avatar: {
    width: 38,
    height: 38,
    borderRadius: 8,
  },
  avatarPlaceholder: {
    width: 38,
    height: 38,
    borderRadius: 8,
    backgroundColor: "#D52B1E",
    justifyContent: "center",
    alignItems: "center",
  },
  avatarPlaceholderText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  playerInfo: {
    flex: 1,
  },
  playerName: {
    fontSize: 17,
    fontWeight: "bold",
    color: "#231716",
    marginBottom: 2,
  },
  playerPosition: {
    fontSize: 15,
    color: "#888",
  },
  jerseyNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#231716",
  },
});
