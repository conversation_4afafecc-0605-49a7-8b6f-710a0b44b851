import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
} from "react-native";
import { useRouter } from "expo-router";
import {
  Ionicons,
  MaterialIcons,
  MaterialCommunityIcons,
} from "@expo/vector-icons";
import { useAuth } from "../../../context/AuthContext";

export default function SettingsScreen() {
  const router = useRouter();
  const { user, isAuthenticated, loginWithGoogle, logout } = useAuth();
  const [googleLoginLoading, setGoogleLoginLoading] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [selectedTheme, setSelectedTheme] = useState<"light" | "dark" | "auto">(
    "auto"
  );
  const [selectedLanguage, setSelectedLanguage] = useState<"en" | "zh">("en");

  const handleGoogleLogin = async () => {
    if (isAuthenticated) {
      // If already authenticated, show logout option
      Alert.alert(
        "Already Logged In",
        `You are logged in as ${
          user?.email || user?.displayName
        }. Do you want to logout?`,
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Logout",
            style: "destructive",
            onPress: handleLogout,
          },
        ]
      );
      return;
    }

    setGoogleLoginLoading(true);
    try {
      console.log("🔍 Starting Google login from Settings...");
      await loginWithGoogle();
      console.log("🔍 Google login completed successfully");
    } catch (error: any) {
      console.error("🔍 Google login error in Settings:", error);
      Alert.alert(
        "Login Error",
        error.message || "Failed to login with Google. Please try again."
      );
    } finally {
      setGoogleLoginLoading(false);
    }
  };

  const handleLogout = async () => {
    setLogoutLoading(true);
    try {
      await logout();
      Alert.alert("Success", "You have been logged out successfully");
    } catch (error: any) {
      Alert.alert("Error", error.message || "Failed to logout");
    } finally {
      setLogoutLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Custom Header with Back Button */}
      <View style={styles.headerContainer}>
        <TouchableOpacity
          style={styles.backHeaderButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color="#fff" />
          <Text style={styles.backHeaderText}>Back</Text>
        </TouchableOpacity>
        <View style={styles.headerTitleWrapper}>
          <Text style={styles.headerTitle}>Settings</Text>
        </View>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.scrollContent}>
        {/* Profile Section */}
        <Text style={styles.sectionHeader}>Profile</Text>
        <View style={styles.sectionCard}>
          {isAuthenticated ? (
            <>
              <View style={styles.profileInfo}>
                <View style={styles.profileIconWrapper}>
                  <MaterialIcons name="person" size={24} color="#346eca" />
                </View>
                <View style={styles.profileDetails}>
                  <Text style={styles.profileName}>
                    {user?.displayName || "User"}
                  </Text>
                  <Text style={styles.profileEmail}>{user?.email}</Text>
                </View>
              </View>
              <View style={styles.divider} />
              <TouchableOpacity
                style={styles.settingsRow}
                onPress={handleLogout}
                disabled={logoutLoading}
              >
                <View style={styles.iconWrapper}>
                  {logoutLoading ? (
                    <ActivityIndicator size="small" color="#FF3B30" />
                  ) : (
                    <MaterialCommunityIcons
                      name="logout-variant"
                      size={20}
                      color="#FF3B30"
                    />
                  )}
                </View>
                <Text style={[styles.settingsText, styles.logoutText]}>
                  Sign Out
                </Text>
              </TouchableOpacity>
            </>
          ) : (
            <TouchableOpacity
              style={styles.settingsRow}
              onPress={handleGoogleLogin}
              disabled={googleLoginLoading}
            >
              <View style={styles.iconWrapper}>
                {googleLoginLoading ? (
                  <ActivityIndicator size="small" color="#346eca" />
                ) : (
                  <MaterialCommunityIcons
                    name="login-variant"
                    size={22}
                    color="#346eca"
                  />
                )}
              </View>
              <Text style={styles.settingsText}>Sign in with Google</Text>
              <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
            </TouchableOpacity>
          )}
        </View>

        {/* Notifications Section */}
        <Text style={styles.sectionHeader}>Notifications</Text>
        <View style={styles.sectionCard}>
          <View style={styles.settingsRow}>
            <View style={styles.iconWrapper}>
              <Ionicons name="notifications" size={20} color="#000" />
            </View>
            <Text style={styles.settingsText}>Enable Notifications</Text>
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: "#E5E5E7", true: "#346eca" }}
              thumbColor={notificationsEnabled ? "#fff" : "#f4f3f4"}
            />
          </View>
        </View>

        {/* Preferences Section */}
        <Text style={styles.sectionHeader}>Preferences</Text>
        <View style={styles.sectionCard}>
          <TouchableOpacity
            style={styles.settingsRow}
            onPress={() => router.push("/more/app-theme" as any)}
          >
            <View style={styles.iconWrapper}>
              <Ionicons name="color-palette" size={20} color="#000" />
            </View>
            <Text style={styles.settingsText}>App Theme</Text>
            <Text style={styles.settingsValue}>
              {selectedTheme === "auto"
                ? "Auto"
                : selectedTheme === "light"
                ? "Light"
                : "Dark"}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </TouchableOpacity>

          <View style={styles.divider} />

          <TouchableOpacity
            style={styles.settingsRow}
            onPress={() => router.push("/more/language" as any)}
          >
            <View style={styles.iconWrapper}>
              <Ionicons name="language" size={20} color="#000" />
            </View>
            <Text style={styles.settingsText}>Language</Text>
            <Text style={styles.settingsValue}>
              {selectedLanguage === "en" ? "English" : "中文"}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </TouchableOpacity>
        </View>

        {/* About Section */}
        <Text style={styles.sectionHeader}>About</Text>
        <View style={styles.sectionCard}>
          <TouchableOpacity
            style={styles.settingsRow}
            onPress={() => router.push("/more/about-app" as any)}
          >
            <View style={styles.iconWrapper}>
              <Ionicons name="information-circle" size={20} color="#000" />
            </View>
            <Text style={styles.settingsText}>About the app</Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </TouchableOpacity>

          <View style={styles.divider} />

          <TouchableOpacity
            style={styles.settingsRow}
            onPress={() => router.push("/more/acknowledgments" as any)}
          >
            <View style={styles.iconWrapper}>
              <Ionicons name="heart" size={20} color="#000" />
            </View>
            <Text style={styles.settingsText}>Acknowledgments</Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </TouchableOpacity>
        </View>

        {/* Legal Section */}
        <Text style={styles.sectionHeader}>Legal</Text>
        <View style={styles.sectionCard}>
          <TouchableOpacity
            style={styles.settingsRow}
            onPress={() => router.push("/more/privacy-policy" as any)}
          >
            <View style={styles.iconWrapper}>
              <Ionicons name="shield-checkmark" size={20} color="#000" />
            </View>
            <Text style={styles.settingsText}>Privacy Policy</Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </TouchableOpacity>

          <View style={styles.divider} />

          <TouchableOpacity
            style={styles.settingsRow}
            onPress={() => router.push("/more/terms-conditions" as any)}
          >
            <View style={styles.iconWrapper}>
              <Ionicons name="document-text" size={20} color="#000" />
            </View>
            <Text style={styles.settingsText}>Terms and Conditions</Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </TouchableOpacity>

          <View style={styles.divider} />

          <TouchableOpacity
            style={styles.settingsRow}
            onPress={() => router.push("/more/license-agreement" as any)}
          >
            <View style={styles.iconWrapper}>
              <Ionicons name="document" size={20} color="#000" />
            </View>
            <Text style={styles.settingsText}>License Agreement</Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </TouchableOpacity>
        </View>
        {/* App Info Section */}
        <View style={styles.appInfoSection}>
          <View style={styles.appIconPlaceholder}></View>
          <Text style={styles.appVersion}>Achieve Hockey 1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 6,
    paddingTop: 10,
    paddingBottom: 5,
    backgroundColor: "#D62828",
    position: "relative",
  },
  headerTitleWrapper: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    pointerEvents: "none",
  },
  headerSpacer: {
    width: 28,
  },
  backHeaderButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  backHeaderText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 2,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
    textAlign: "center",
  },
  scrollContent: {
    flex: 1,
    paddingTop: 15,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: "bold",
    color: "black",
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 5,
  },
  sectionCard: {
    backgroundColor: "white",
    borderRadius: 7,
    marginHorizontal: 10,
    marginVertical: 5,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
    shadowOffset: { width: 0, height: 0 },
  },
  settingsRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 14,
  },
  iconWrapper: {
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  settingsText: {
    fontSize: 16,
    color: "black",
    flex: 1,
  },
  settingsValue: {
    fontSize: 16,
    color: "#8E8E93",
    marginRight: 8,
  },
  divider: {
    height: 1,
    backgroundColor: "#E5E5E7",
    marginLeft: 12,
  },
  logoutText: {
    color: "#FF3B30",
  },
  profileInfo: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  profileIconWrapper: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#F0F1F5",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    fontSize: 16,
    fontWeight: "600",
    color: "black",
  },
  profileEmail: {
    fontSize: 14,
    color: "#666",
    marginTop: 2,
  },
  aboutContent: {
    padding: 16,
    alignItems: "center",
  },
  aboutTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "black",
    marginBottom: 4,
  },
  aboutVersion: {
    fontSize: 14,
    color: "#666",
    marginBottom: 12,
  },
  aboutDescription: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    lineHeight: 20,
    marginBottom: 12,
  },
  aboutAcknowledgments: {
    fontSize: 12,
    color: "#999",
    textAlign: "center",
    lineHeight: 18,
  },
  appInfoSection: {
    alignItems: "center",
    paddingVertical: 20,
  },
  appIconPlaceholder: {
    width: 25,
    height: 25,
    borderRadius: 7,
    backgroundColor: "#F0F0F0",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
    borderWidth: 1,
    borderColor: "#E5E5E7",
  },
  appVersion: {
    fontSize: 12,
    color: "grey",
  },
});
