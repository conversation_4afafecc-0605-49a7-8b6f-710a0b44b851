import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

export default function AppThemeScreen() {
  const router = useRouter();
  const [selectedTheme, setSelectedTheme] = useState<"light" | "dark" | "auto">("auto");

  const handleThemeSelection = (theme: "light" | "dark" | "auto") => {
    setSelectedTheme(theme);
    // Here you would typically persist the theme preference
    // and apply it to the app
    // For now, we'll just update the state and go back
    setTimeout(() => {
      router.back();
    }, 300);
  };

  const themes = [
    { key: "light" as const, label: "Light", description: "Use light theme" },
    { key: "dark" as const, label: "Dark", description: "Use dark theme" },
    { key: "auto" as const, label: "Auto", description: "Follow system setting" },
  ];

  return (
    <SafeAreaView style={styles.container}>
      {/* Custom Header with Exit Button */}
      <View style={styles.headerContainer}>
        <View style={styles.headerSpacer} />
        <Text style={styles.headerTitle}>App Theme</Text>
        <TouchableOpacity
          style={styles.exitButton}
          onPress={() => router.back()}
        >
          <View style={styles.roundXButton}>
            <Ionicons name="close" size={17} color="#000" />
          </View>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContent}>
        <View style={styles.contentContainer}>
          <View style={styles.contentSection}>
            <Text style={styles.sectionDescription}>
              Choose how the app should appear. Auto will follow your device's system setting.
            </Text>
            
            {themes.map((theme) => (
              <TouchableOpacity
                key={theme.key}
                style={[
                  styles.themeOption,
                  selectedTheme === theme.key && styles.themeOptionSelected
                ]}
                onPress={() => handleThemeSelection(theme.key)}
              >
                <View style={styles.themeOptionContent}>
                  <Text style={[
                    styles.themeOptionTitle,
                    selectedTheme === theme.key && styles.themeOptionTitleSelected
                  ]}>
                    {theme.label}
                  </Text>
                  <Text style={styles.themeOptionDescription}>
                    {theme.description}
                  </Text>
                </View>
                {selectedTheme === theme.key && (
                  <Ionicons name="checkmark" size={20} color="#346eca" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 12,
    paddingTop: 10,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    backgroundColor: "#fff",
  },
  headerSpacer: {
    width: 28,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000",
    flex: 1,
    textAlign: "center",
  },
  exitButton: {
    paddingHorizontal: 8,
    paddingVertical: 5,
  },
  roundXButton: {
    width: 25,
    height: 25,
    borderRadius: 14,
    backgroundColor: "#EEEEEE",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  scrollContent: {
    flex: 1,
  },
  contentContainer: {
    backgroundColor: "#FFFFFF",
    marginHorizontal: 10,
    marginTop: 15,
    borderRadius: 7,
    shadowColor: "#000",
    shadowOpacity: 0.07,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 1 },
    elevation: 1,
  },
  contentSection: {
    padding: 15,
  },
  sectionDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 20,
    textAlign: "center",
  },
  themeOption: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginVertical: 4,
    borderWidth: 1,
    borderColor: "#E5E5E7",
  },
  themeOptionSelected: {
    backgroundColor: "#F0F8FF",
    borderColor: "#346eca",
  },
  themeOptionContent: {
    flex: 1,
  },
  themeOptionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "black",
    marginBottom: 2,
  },
  themeOptionTitleSelected: {
    color: "#346eca",
  },
  themeOptionDescription: {
    fontSize: 14,
    color: "#666",
  },
});
