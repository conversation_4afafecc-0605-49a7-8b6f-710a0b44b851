import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

export default function TermsConditionsScreen() {
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container}>
      {/* Custom Header with Exit Button */}
      <View style={styles.headerContainer}>
        <View style={styles.headerSpacer} />
        <Text style={styles.headerTitle}>Terms and Conditions</Text>
        <TouchableOpacity
          style={styles.exitButton}
          onPress={() => router.back()}
        >
          <View style={styles.roundXButton}>
            <Ionicons name="close" size={17} color="#000" />
          </View>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContent}>
        <View style={styles.contentContainer}>
          <View style={styles.contentSection}>
            <Text style={styles.lastUpdated}>
              Last updated: January 2025
            </Text>

            <Text style={styles.sectionTitle}>Acceptance of Terms</Text>
            <Text style={styles.description}>
              By accessing and using the Achieve Hockey app, you accept and agree to be bound by 
              the terms and provision of this agreement.
            </Text>

            <Text style={styles.sectionTitle}>Use License</Text>
            <Text style={styles.description}>
              Permission is granted to temporarily use the Achieve Hockey app for personal, 
              non-commercial transitory viewing only. This is the grant of a license, not a 
              transfer of title.
            </Text>

            <Text style={styles.sectionTitle}>User Responsibilities</Text>
            <Text style={styles.description}>
              Users are responsible for maintaining the confidentiality of their account information 
              and for all activities that occur under their account. Users must provide accurate 
              and complete information when registering for tournaments or events.
            </Text>

            <Text style={styles.sectionTitle}>Prohibited Uses</Text>
            <Text style={styles.description}>
              You may not use the app for any unlawful purpose or to solicit others to perform 
              unlawful acts. You may not violate any local, state, national, or international law.
            </Text>

            <Text style={styles.sectionTitle}>Limitation of Liability</Text>
            <Text style={styles.description}>
              In no event shall Achieve Hockey or its suppliers be liable for any damages arising 
              out of the use or inability to use the materials on the app.
            </Text>

            <Text style={styles.sectionTitle}>Modifications</Text>
            <Text style={styles.description}>
              Achieve Hockey may revise these terms of service at any time without notice. 
              By using this app, you are agreeing to be bound by the current version of these terms.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 12,
    paddingTop: 10,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    backgroundColor: "#fff",
  },
  headerSpacer: {
    width: 28,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000",
    flex: 1,
    textAlign: "center",
  },
  exitButton: {
    paddingHorizontal: 8,
    paddingVertical: 5,
  },
  roundXButton: {
    width: 25,
    height: 25,
    borderRadius: 14,
    backgroundColor: "#EEEEEE",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  scrollContent: {
    flex: 1,
  },
  contentContainer: {
    backgroundColor: "#FFFFFF",
    marginHorizontal: 10,
    marginTop: 15,
    borderRadius: 7,
    shadowColor: "#000",
    shadowOpacity: 0.07,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 1 },
    elevation: 1,
  },
  contentSection: {
    padding: 15,
  },
  lastUpdated: {
    fontSize: 12,
    color: "#999",
    marginBottom: 20,
    textAlign: "center",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#231716",
    marginTop: 20,
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: "#231716",
    lineHeight: 22,
    marginBottom: 16,
  },
});
