import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

export default function LicenseAgreementScreen() {
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container}>
      {/* Custom Header with Exit Button */}
      <View style={styles.headerContainer}>
        <View style={styles.headerSpacer} />
        <Text style={styles.headerTitle}>License Agreement</Text>
        <TouchableOpacity
          style={styles.exitButton}
          onPress={() => router.back()}
        >
          <View style={styles.roundXButton}>
            <Ionicons name="close" size={17} color="#000" />
          </View>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContent}>
        <View style={styles.contentContainer}>
          <View style={styles.contentSection}>
            <Text style={styles.lastUpdated}>
              Last updated: January 2025
            </Text>

            <Text style={styles.sectionTitle}>Software License</Text>
            <Text style={styles.description}>
              This License Agreement ("Agreement") is a legal agreement between you and Achieve Hockey 
              for the use of the Achieve Hockey mobile application ("Software").
            </Text>

            <Text style={styles.sectionTitle}>Grant of License</Text>
            <Text style={styles.description}>
              Subject to the terms of this Agreement, Achieve Hockey grants you a limited, 
              non-exclusive, non-transferable license to use the Software on your mobile device.
            </Text>

            <Text style={styles.sectionTitle}>Restrictions</Text>
            <Text style={styles.description}>
              You may not copy, modify, distribute, sell, or lease any part of the Software. 
              You may not reverse engineer or attempt to extract the source code of the Software.
            </Text>

            <Text style={styles.sectionTitle}>Intellectual Property</Text>
            <Text style={styles.description}>
              The Software and all intellectual property rights therein are and shall remain 
              the property of Achieve Hockey and its licensors.
            </Text>

            <Text style={styles.sectionTitle}>Third-Party Components</Text>
            <Text style={styles.description}>
              The Software may include third-party components that are subject to separate 
              license terms. These components are licensed under their respective open source licenses.
            </Text>

            <Text style={styles.sectionTitle}>Termination</Text>
            <Text style={styles.description}>
              This license is effective until terminated. Your rights under this license will 
              terminate automatically without notice if you fail to comply with any term of this Agreement.
            </Text>

            <Text style={styles.sectionTitle}>Disclaimer</Text>
            <Text style={styles.description}>
              The Software is provided "as is" without warranty of any kind. Achieve Hockey 
              disclaims all warranties, express or implied, including but not limited to 
              merchantability and fitness for a particular purpose.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 12,
    paddingTop: 10,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    backgroundColor: "#fff",
  },
  headerSpacer: {
    width: 28,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000",
    flex: 1,
    textAlign: "center",
  },
  exitButton: {
    paddingHorizontal: 8,
    paddingVertical: 5,
  },
  roundXButton: {
    width: 25,
    height: 25,
    borderRadius: 14,
    backgroundColor: "#EEEEEE",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  scrollContent: {
    flex: 1,
  },
  contentContainer: {
    backgroundColor: "#FFFFFF",
    marginHorizontal: 10,
    marginTop: 15,
    borderRadius: 7,
    shadowColor: "#000",
    shadowOpacity: 0.07,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 1 },
    elevation: 1,
  },
  contentSection: {
    padding: 15,
  },
  lastUpdated: {
    fontSize: 12,
    color: "#999",
    marginBottom: 20,
    textAlign: "center",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#231716",
    marginTop: 20,
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: "#231716",
    lineHeight: 22,
    marginBottom: 16,
  },
});
