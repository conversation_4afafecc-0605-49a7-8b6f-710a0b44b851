import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

export default function AboutAppScreen() {
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container}>
      {/* Custom Header with Exit Button */}
      <View style={styles.headerContainer}>
        <View style={styles.headerSpacer} />
        <Text style={styles.headerTitle}>About the App</Text>
        <TouchableOpacity
          style={styles.exitButton}
          onPress={() => router.back()}
        >
          <View style={styles.roundXButton}>
            <Ionicons name="close" size={17} color="#000" />
          </View>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContent}>
        {/* Version Section */}
        <Text style={styles.sectionTitle}>Version</Text>
        <View style={styles.infoContainer}>
          <View style={styles.infoContent}>
            <Text style={styles.infoText}>1.0.0</Text>
          </View>
        </View>

        {/* Developers Section */}
        <Text style={styles.sectionTitle}>Developers</Text>
        <View style={styles.infoContainer}>
          <View style={styles.developerContainer}>
            <Text style={styles.infoText}>
              Danial Utegenov - fontend dev, UI/UX
            </Text>
            <Text style={styles.infoText}>Vlad Yun – backend dev</Text>
            <Text style={styles.infoText}>
              Solomon Kim – frontend dev, legal issues{" "}
            </Text>
          </View>
        </View>

        <Text style={styles.copyright}>
          © 2025 Achieve Hockey. All rights reserved.
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 12,
    paddingTop: 10,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    backgroundColor: "#fff",
  },
  headerSpacer: {
    width: 28,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#231716",
    flex: 1,
    textAlign: "center",
  },
  exitButton: {
    paddingHorizontal: 8,
    paddingVertical: 5,
  },
  roundXButton: {
    width: 25,
    height: 25,
    borderRadius: 14,
    backgroundColor: "#EEEEEE",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  scrollContent: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: "600",
    color: "#231716",
    marginTop: 15,
    marginBottom: 8,
    marginLeft: 18,
  },
  infoContainer: {
    backgroundColor: "#fff",
    marginHorizontal: 10,
    borderRadius: 7,
    shadowColor: "#000",
    shadowOpacity: 0.07,
    shadowRadius: 3,
    shadowOffset: { width: 0, height: 1 },
    elevation: 1,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  infoContent: {
    flexDirection: "column",
    padding: 18,
    alignItems: "flex-start",
  },
  infoText: {
    fontSize: 16,
    color: "#231716",
    fontWeight: "400",
  },
  developerContainer: {
    gap: 12,
    padding: 18,
  },
  copyright: {
    fontSize: 12,
    color: "#999",
    textAlign: "center",
    marginTop: 30,
    marginBottom: 10,
  },
});
