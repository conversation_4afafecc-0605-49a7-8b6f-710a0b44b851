import { Stack } from "expo-router";

export default function MoreLayout() {
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: "#D62828",
        },
        headerTintColor: "#fff",
        headerTitleStyle: {
          fontSize: 18,
        },
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="registration"
        options={{
          headerShown: false,
          title: "Team registration",
          headerBackTitle: "Back",
        }}
      />
      <Stack.Screen
        name="ai-chatbot"
        options={{
          title: "AI Chatbot",
          headerBackTitle: "Back",
        }}
      />
      <Stack.Screen
        name="about-us"
        options={{
          headerShown: false,
          presentation: "modal",
          contentStyle: {
            backgroundColor: "#F7F7F7",
          },
        }}
      />
      <Stack.Screen
        name="contact-info"
        options={{
          headerShown: false,
          presentation: "modal",
          contentStyle: {
            backgroundColor: "#F7F7F7",
          },
        }}
      />
      <Stack.Screen
        name="faq"
        options={{
          headerShown: false,
          presentation: "modal",
          contentStyle: {
            backgroundColor: "#F7F7F7",
          },
        }}
      />
      <Stack.Screen
        name="navigation-for-teams"
        options={{
          headerShown: false,
          presentation: "modal",
          contentStyle: {
            backgroundColor: "#F7F7F7",
          },
        }}
      />
      <Stack.Screen
        name="settings"
        options={{
          headerShown: false,
          contentStyle: {
            backgroundColor: "#F7F7F7",
          },
        }}
      />
      <Stack.Screen
        name="app-theme"
        options={{
          headerShown: false,
          presentation: "modal",
          contentStyle: {
            backgroundColor: "#F0F1F5",
          },
        }}
      />
      <Stack.Screen
        name="language"
        options={{
          headerShown: false,
          presentation: "modal",
          contentStyle: {
            backgroundColor: "#F0F1F5",
          },
        }}
      />
      <Stack.Screen
        name="privacy-policy"
        options={{
          headerShown: false,
          presentation: "modal",
          contentStyle: {
            backgroundColor: "#F0F1F5",
          },
        }}
      />
      <Stack.Screen
        name="terms-conditions"
        options={{
          headerShown: false,
          presentation: "modal",
          contentStyle: {
            backgroundColor: "#F0F1F5",
          },
        }}
      />
      <Stack.Screen
        name="license-agreement"
        options={{
          headerShown: false,
          presentation: "modal",
          contentStyle: {
            backgroundColor: "#F0F1F5",
          },
        }}
      />
      <Stack.Screen
        name="about-app"
        options={{
          headerShown: false,
          presentation: "modal",
          contentStyle: {
            backgroundColor: "#F0F1F5",
          },
        }}
      />
      <Stack.Screen
        name="acknowledgments"
        options={{
          headerShown: false,
          presentation: "modal",
          contentStyle: {
            backgroundColor: "#F0F1F5",
          },
        }}
      />
    </Stack>
  );
}
