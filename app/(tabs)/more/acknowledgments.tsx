import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

export default function AcknowledgmentsScreen() {
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container}>
      {/* Custom Header with Exit Button */}
      <View style={styles.headerContainer}>
        <View style={styles.headerSpacer} />
        <Text style={styles.headerTitle}>Acknowledgments</Text>
        <TouchableOpacity
          style={styles.exitButton}
          onPress={() => router.back()}
        >
          <View style={styles.roundXButton}>
            <Ionicons name="close" size={17} color="#000" />
          </View>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContent}>
        <View style={styles.contentContainer}>
          <View style={styles.contentSection}>
            <Text style={styles.introText}>
              We extend our heartfelt gratitude to all the individuals, organizations, 
              and communities that have made Achieve Hockey possible.
            </Text>

            <Text style={styles.sectionTitle}>Hong Kong Puck Hockey League</Text>
            <Text style={styles.description}>
              Special thanks to the Hong Kong Puck Hockey League (HKPHL) for their 
              continuous support, guidance, and partnership in developing this application. 
              Their dedication to promoting hockey in Hong Kong has been instrumental 
              in shaping this project.
            </Text>

            <Text style={styles.sectionTitle}>Development Team</Text>
            <Text style={styles.description}>
              Our passionate development team who worked tirelessly to bring this vision to life, 
              combining technical expertise with a deep love for the sport of hockey.
            </Text>

            <Text style={styles.sectionTitle}>Hockey Community</Text>
            <Text style={styles.description}>
              The entire hockey community in Hong Kong and beyond, including players, coaches, 
              referees, and fans who provided valuable feedback and support throughout the 
              development process.
            </Text>

            <Text style={styles.sectionTitle}>Open Source Libraries</Text>
            <Text style={styles.description}>
              We acknowledge the following open source libraries and frameworks that 
              made this application possible:
            </Text>
            <Text style={styles.libraryList}>
              • React Native - Mobile app framework{'\n'}
              • Expo - Development platform{'\n'}
              • Firebase - Backend services{'\n'}
              • React Navigation - Navigation library{'\n'}
              • Vector Icons - Icon library{'\n'}
              • And many other amazing open source projects
            </Text>

            <Text style={styles.sectionTitle}>Beta Testers</Text>
            <Text style={styles.description}>
              Our dedicated beta testers who provided invaluable feedback and helped 
              identify issues before the official release.
            </Text>

            <Text style={styles.sectionTitle}>Partners & Sponsors</Text>
            <Text style={styles.description}>
              All our partners and sponsors who believed in our mission and provided 
              the resources necessary to make this project a reality.
            </Text>

            <Text style={styles.closingText}>
              Thank you for being part of the Achieve Hockey journey. Together, 
              we're building a stronger hockey community! 🏒
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 12,
    paddingTop: 10,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    backgroundColor: "#fff",
  },
  headerSpacer: {
    width: 28,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000",
    flex: 1,
    textAlign: "center",
  },
  exitButton: {
    paddingHorizontal: 8,
    paddingVertical: 5,
  },
  roundXButton: {
    width: 25,
    height: 25,
    borderRadius: 14,
    backgroundColor: "#EEEEEE",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  scrollContent: {
    flex: 1,
  },
  contentContainer: {
    backgroundColor: "#FFFFFF",
    marginHorizontal: 10,
    marginTop: 15,
    borderRadius: 7,
    shadowColor: "#000",
    shadowOpacity: 0.07,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 1 },
    elevation: 1,
  },
  contentSection: {
    padding: 15,
  },
  introText: {
    fontSize: 16,
    color: "#231716",
    lineHeight: 22,
    marginBottom: 25,
    textAlign: "center",
    fontStyle: "italic",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#231716",
    marginTop: 20,
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: "#231716",
    lineHeight: 22,
    marginBottom: 16,
  },
  libraryList: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 16,
    marginLeft: 10,
  },
  closingText: {
    fontSize: 16,
    color: "#346eca",
    lineHeight: 22,
    marginTop: 20,
    marginBottom: 10,
    textAlign: "center",
    fontWeight: "500",
  },
});
