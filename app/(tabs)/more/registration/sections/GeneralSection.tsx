import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface GeneralData {
  representativeName: string;
  email: string;
  phone: string;
  completed: boolean;
}

interface Props {
  data: GeneralData;
  onUpdate: (data: Partial<GeneralData>) => void;
}

export default function GeneralSection({ data, onUpdate }: Props) {
  const [formData, setFormData] = useState({
    representativeName: data.representativeName,
    email: data.email,
    phone: data.phone,
  });

  const [errors, setErrors] = useState({
    representativeName: "",
    email: "",
    phone: "",
  });

  // Track if a field has been touched
  const [touched, setTouched] = useState({
    representativeName: false,
    email: false,
    phone: false,
  });

  useEffect(() => {
    validateAndUpdate();
  }, [formData]);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string) => {
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
    return phoneRegex.test(phone);
  };

  const validateAndUpdate = () => {
    const newErrors = {
      representativeName: "",
      email: "",
      phone: "",
    };

    // Validate representative name
    if (!formData.representativeName.trim()) {
      newErrors.representativeName = "Representative name is required";
    } else if (formData.representativeName.trim().length < 2) {
      newErrors.representativeName = "Name must be at least 2 characters";
    }

    // Validate email
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!validateEmail(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Validate phone
    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!validatePhone(formData.phone)) {
      newErrors.phone = "Please enter a valid phone number";
    }

    setErrors(newErrors);

    // Check if form is completed
    const isCompleted =
      formData.representativeName.trim() !== "" &&
      formData.email.trim() !== "" &&
      formData.phone.trim() !== "" &&
      !newErrors.representativeName &&
      !newErrors.email &&
      !newErrors.phone;

    // Update parent component
    onUpdate({
      ...formData,
      completed: isCompleted,
    });
  };

  const updateField = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
    setTouched((prev) => ({
      ...prev,
      [field]: true,
    }));
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>General Information</Text>
          <Text style={styles.subtitle}>
            Please provide the team representative's contact information
          </Text>
        </View>

        <View style={styles.form}>
          {/* Representative Name */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>
              Team Representative Name <Text style={styles.required}>*</Text>
            </Text>
            <TextInput
              style={[
                styles.input,
                touched.representativeName &&
                  errors.representativeName &&
                  styles.inputError,
              ]}
              value={formData.representativeName}
              onChangeText={(value) => updateField("representativeName", value)}
              placeholder="Enter full name"
              placeholderTextColor="#999"
              autoCapitalize="words"
              onBlur={() =>
                setTouched((prev) => ({ ...prev, representativeName: true }))
              }
            />
            {touched.representativeName && errors.representativeName ? (
              <Text style={styles.errorText}>{errors.representativeName}</Text>
            ) : null}
          </View>

          {/* Email */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>
              Contact Email <Text style={styles.required}>*</Text>
            </Text>
            <TextInput
              style={[
                styles.input,
                touched.email && errors.email && styles.inputError,
              ]}
              value={formData.email}
              onChangeText={(value) => updateField("email", value)}
              placeholder="Enter email address"
              placeholderTextColor="#999"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              onBlur={() => setTouched((prev) => ({ ...prev, email: true }))}
            />
            {touched.email && errors.email ? (
              <Text style={styles.errorText}>{errors.email}</Text>
            ) : null}
          </View>

          {/* Phone */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>
              Contact Phone <Text style={styles.required}>*</Text>
            </Text>
            <TextInput
              style={[
                styles.input,
                touched.phone && errors.phone && styles.inputError,
              ]}
              value={formData.phone}
              onChangeText={(value) => updateField("phone", value)}
              placeholder="Enter phone number"
              placeholderTextColor="#999"
              keyboardType="phone-pad"
              onBlur={() => setTouched((prev) => ({ ...prev, phone: true }))}
            />
            {touched.phone && errors.phone ? (
              <Text style={styles.errorText}>{errors.phone}</Text>
            ) : null}
          </View>
        </View>

        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                { width: data.completed ? "100%" : "0%" },
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {data.completed
              ? "✓ Section completed"
              : "Please complete all required fields"}
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  content: {
    padding: 16,
  },
  header: {
    alignItems: "flex-start",
    marginBottom: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#231716",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: "#555",
    textAlign: "left",
    lineHeight: 22,
  },
  form: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 1,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: "#231716",
    marginBottom: 8,
  },
  required: {
    color: "#D62828",
  },
  input: {
    borderWidth: 1,
    borderColor: "#E5E5E7",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: "#fff",
  },
  inputError: {
    borderColor: "#D62828",
  },
  errorText: {
    color: "#D62828",
    fontSize: 14,
    marginTop: 4,
  },
  progressContainer: {
    marginTop: 30,
    alignItems: "center",
  },
  progressBar: {
    width: "100%",
    height: 4,
    backgroundColor: "#E5E5E7",
    borderRadius: 2,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#4CAF50",
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: "#666",
    marginTop: 8,
    textAlign: "center",
  },
});
