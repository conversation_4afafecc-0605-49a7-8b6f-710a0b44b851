import React, { useState, useEffect } from "react";
import { useNavigation } from "@react-navigation/native";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import AddMemberModal, { MemberData } from "../modals/AddMemberModal";

// Registration data interface
interface RegistrationData {
  general: {
    representativeName: string;
    email: string;
    phone: string;
    completed: boolean;
  };
  tournament: {
    eventType: string;
    eventId: string;
    completed: boolean;
  };
  team: {
    name: string;
    logo: string | null;
    ageGroup: string;
    division: string;
    completed: boolean;
  };
  roster: {
    forwards: Array<{
      name: string;
      photo: string | null;
      jerseyNumber?: string;
    }>;
    defence: Array<{
      name: string;
      photo: string | null;
      jerseyNumber?: string;
    }>;
    goalies: Array<{
      name: string;
      photo: string | null;
      jerseyNumber?: string;
    }>;
    coaches: Array<{ name: string; photo: string | null; role?: string }>;
    completed: boolean;
  };
}

interface Player {
  name: string;
  photo: string | null;
  jerseyNumber?: string;
}

interface Coach {
  name: string;
  photo: string | null;
  role?: string;
}

interface RosterData {
  forwards: Player[];
  defence: Player[];
  goalies: Player[];
  coaches: Coach[];
  completed: boolean;
}

interface Props {
  data: RosterData;
  onUpdate: (data: Partial<RosterData>) => void;
  canAccess: boolean;
  allData: RegistrationData;
}

export default function RosterSection({
  data,
  onUpdate,
  canAccess,
  allData,
}: Props) {
  const navigation = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const [currentPosition, setCurrentPosition] = useState<
    "forwards" | "defence" | "goalies" | "coaches"
  >("forwards");
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingMember, setEditingMember] = useState<MemberData | null>(null);

  useEffect(() => {
    validateAndUpdate();
  }, [data.forwards, data.defence, data.goalies, data.coaches]);

  const validateAndUpdate = () => {
    // Check if at least one player/coach has been added with name and photo
    const hasValidMembers = [
      ...data.forwards,
      ...data.defence,
      ...data.goalies,
      ...data.coaches,
    ].some((member) => member.name.trim() !== "" && member.photo !== null);

    onUpdate({
      completed: hasValidMembers,
    });
  };

  const openAddModal = (position: typeof currentPosition) => {
    setCurrentPosition(position);
    setEditingIndex(null);
    setEditingMember(null);
    setModalVisible(true);
  };

  const openEditModal = (position: typeof currentPosition, index: number) => {
    setCurrentPosition(position);
    setEditingIndex(index);
    const member = data[position][index];
    setEditingMember({
      name: member.name,
      photo: member.photo,
      jerseyNumber: (member as Player).jerseyNumber || "",
      role: (member as Coach).role || "",
    });
    setModalVisible(true);
  };

  const saveMember = (memberData: MemberData) => {
    const formattedMember = {
      name: memberData.name.trim(),
      photo: memberData.photo,
      ...(currentPosition === "coaches"
        ? { role: memberData.role }
        : { jerseyNumber: memberData.jerseyNumber }),
    };

    const updatedData = { ...data };

    if (editingIndex !== null) {
      // Edit existing member
      updatedData[currentPosition][editingIndex] = formattedMember as any;
    } else {
      // Add new member
      updatedData[currentPosition].push(formattedMember as any);
    }

    // Sort alphabetically by name
    updatedData[currentPosition].sort((a, b) => a.name.localeCompare(b.name));

    // Calculate completion status
    const hasValidMembers = [
      ...updatedData.forwards,
      ...updatedData.defence,
      ...updatedData.goalies,
      ...updatedData.coaches,
    ].some((member) => member.name.trim() !== "" && member.photo !== null);

    onUpdate({
      ...updatedData,
      completed: hasValidMembers,
    });
    setModalVisible(false);
  };

  const deleteMember = (position: typeof currentPosition, index: number) => {
    Alert.alert(
      "Delete Member",
      "Are you sure you want to remove this member?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => {
            const updatedData = { ...data };
            updatedData[position].splice(index, 1);

            // Calculate completion status
            const hasValidMembers = [
              ...updatedData.forwards,
              ...updatedData.defence,
              ...updatedData.goalies,
              ...updatedData.coaches,
            ].some(
              (member) => member.name.trim() !== "" && member.photo !== null
            );

            onUpdate({
              ...updatedData,
              completed: hasValidMembers,
            });
          },
        },
      ]
    );
  };

  const handleSubmission = async () => {
    try {
      Alert.alert(
        "Submit Registration",
        "Are you sure you want to submit your team registration? This action cannot be undone.",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Submit",
            style: "default",
            onPress: async () => {
              // Here you would typically send the data to your backend
              // For now, we'll just show a success message
              Alert.alert(
                "Registration Submitted!",
                "Your team registration has been submitted successfully. You will receive a confirmation email shortly.",
                [
                  {
                    text: "OK",
                    onPress: () => {
                      // Navigate back to the 'more' tab after successful submission
                      console.log("Registration submitted:", allData);
                      // Use reset to ensure navigation to the root of the 'more' tab
                      (navigation as any).reset({
                        index: 0,
                        routes: [{ name: "more" }],
                      });
                    },
                  },
                ]
              );
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert("Error", "Failed to submit registration. Please try again.");
    }
  };

  const renderMemberCard = (
    member: Player | Coach,
    position: typeof currentPosition,
    index: number
  ) => (
    <View key={`${position}-${index}`} style={styles.memberCard}>
      <Image source={{ uri: member.photo! }} style={styles.memberPhoto} />
      <View style={styles.memberInfo}>
        <Text style={styles.memberName}>{member.name}</Text>
        {position !== "coaches" && (member as Player).jerseyNumber && (
          <Text style={styles.memberDetail}>
            #{(member as Player).jerseyNumber}
          </Text>
        )}
        {position === "coaches" && (member as Coach).role && (
          <Text style={styles.memberDetail}>{(member as Coach).role}</Text>
        )}
      </View>
      <View style={styles.memberActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => openEditModal(position, index)}
        >
          <Ionicons name="pencil" size={16} color="#666" />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => deleteMember(position, index)}
        >
          <Ionicons name="trash" size={16} color="#D62828" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderPositionSection = (
    title: string,
    position: typeof currentPosition,
    icon: string,
    members: (Player | Coach)[]
  ) => (
    <View style={styles.positionSection}>
      <View style={styles.positionHeader}>
        <View style={styles.positionTitleContainer}>
          <Ionicons name={icon as any} size={20} color="#D62828" />
          <Text style={styles.positionTitle}>{title}</Text>
          <Text style={styles.memberCount}>({members.length})</Text>
        </View>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => openAddModal(position)}
        >
          <Ionicons name="add" size={20} color="#fff" />
        </TouchableOpacity>
      </View>

      {members.length > 0 ? (
        <View style={styles.membersContainer}>
          {members.map((member, index) =>
            renderMemberCard(member, position, index)
          )}
        </View>
      ) : (
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>
            No {title.toLowerCase()} added yet
          </Text>
        </View>
      )}
    </View>
  );

  if (!canAccess) {
    return (
      <View style={styles.lockedContainer}>
        <Ionicons name="lock-closed" size={48} color="#999" />
        <Text style={styles.lockedTitle}>Complete Previous Section</Text>
        <Text style={styles.lockedText}>
          Please complete the Team section before proceeding to Roster
          management.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Team Roster</Text>
            <Text style={styles.subtitle}>
              Add players and coaches to your team roster
            </Text>
          </View>

          {renderPositionSection(
            "Forwards",
            "forwards",
            "arrow-up",
            data.forwards
          )}
          {renderPositionSection("Defence", "defence", "shield", data.defence)}
          {renderPositionSection(
            "Goalies",
            "goalies",
            "hand-left",
            data.goalies
          )}
          {renderPositionSection("Coaches", "coaches", "school", data.coaches)}

          {/* Progress Indicator */}
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: data.completed ? "100%" : "0%" },
                ]}
              />
            </View>
            <Text style={styles.progressText}>
              {data.completed
                ? "✓ Section completed"
                : "Add at least one team member with photo"}
            </Text>
          </View>

          {/* Submission Section */}
          {allData.general.completed &&
            allData.tournament.completed &&
            allData.team.completed &&
            data.completed && (
              <View style={styles.submissionSection}>
                <Text style={styles.submissionTitle}>Ready to Submit!</Text>
                <Text style={styles.submissionSubtitle}>
                  Review your registration details and submit your team
                  registration.
                </Text>

                <View style={styles.summaryCard}>
                  <Text style={styles.summaryTitle}>Registration Summary</Text>

                  <View style={styles.summaryItem}>
                    <Text style={styles.summaryLabel}>Representative:</Text>
                    <Text style={styles.summaryValue}>
                      {allData.general.representativeName}
                    </Text>
                  </View>

                  <View style={styles.summaryItem}>
                    <Text style={styles.summaryLabel}>Team Name:</Text>
                    <Text style={styles.summaryValue}>{allData.team.name}</Text>
                  </View>

                  <View style={styles.summaryItem}>
                    <Text style={styles.summaryLabel}>Age Group:</Text>
                    <Text style={styles.summaryValue}>
                      {allData.team.ageGroup}
                    </Text>
                  </View>

                  <View style={styles.summaryItem}>
                    <Text style={styles.summaryLabel}>Division:</Text>
                    <Text style={styles.summaryValue}>
                      {allData.team.division}
                    </Text>
                  </View>

                  <View style={styles.summaryItem}>
                    <Text style={styles.summaryLabel}>Total Members:</Text>
                    <Text style={styles.summaryValue}>
                      {data.forwards.length +
                        data.defence.length +
                        data.goalies.length +
                        data.coaches.length}
                    </Text>
                  </View>
                </View>

                <TouchableOpacity
                  style={styles.submitButton}
                  onPress={handleSubmission}
                >
                  <Text style={styles.submitButtonText}>
                    Submit Registration
                  </Text>
                </TouchableOpacity>
              </View>
            )}
        </View>
      </ScrollView>

      <AddMemberModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onSave={saveMember}
        position={currentPosition}
        editingMember={editingMember}
        isEditing={editingIndex !== null}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    alignItems: "flex-start",
    marginBottom: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#231716",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: "#555",
    textAlign: "left",
    lineHeight: 22,
  },
  positionSection: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 1,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  positionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  positionTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  positionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#231716",
    marginLeft: 8,
  },
  memberCount: {
    fontSize: 14,
    color: "#666",
    marginLeft: 4,
  },
  addButton: {
    backgroundColor: "#D62828",
    borderRadius: 20,
    width: 36,
    height: 36,
    justifyContent: "center",
    alignItems: "center",
  },
  membersContainer: {
    gap: 8,
  },
  memberCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    backgroundColor: "#F8F9FA",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E5E5E7",
  },
  memberPhoto: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
  },
  memberDetail: {
    fontSize: 14,
    color: "#666",
    marginTop: 2,
  },
  memberActions: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#E5E5E7",
  },
  emptyState: {
    padding: 20,
    alignItems: "center",
  },
  emptyText: {
    fontSize: 14,
    color: "#999",
    fontStyle: "italic",
  },
  progressContainer: {
    marginTop: 30,
    alignItems: "center",
  },
  progressBar: {
    width: "100%",
    height: 4,
    backgroundColor: "#E5E5E7",
    borderRadius: 2,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#4CAF50",
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: "#666",
    marginTop: 8,
    textAlign: "center",
  },
  lockedContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
    backgroundColor: "#F0F1F5",
  },
  lockedTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#999",
    marginTop: 16,
    marginBottom: 8,
  },
  lockedText: {
    fontSize: 16,
    color: "#999",
    textAlign: "center",
    lineHeight: 22,
  },

  submissionSection: {
    marginTop: 30,
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 2,
    borderColor: "#4CAF50",
  },
  submissionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#4CAF50",
    textAlign: "center",
    marginBottom: 8,
  },
  submissionSubtitle: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 22,
  },
  summaryCard: {
    backgroundColor: "#F8F9FA",
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 16,
    textAlign: "center",
  },
  summaryItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E7",
  },
  summaryLabel: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
  },
  summaryValue: {
    fontSize: 14,
    color: "#1a1a1a",
    fontWeight: "600",
    flex: 1,
    textAlign: "right",
  },
  submitButton: {
    backgroundColor: "#4CAF50",
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
  },
});
