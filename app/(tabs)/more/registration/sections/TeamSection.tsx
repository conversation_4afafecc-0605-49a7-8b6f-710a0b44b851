import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import AgeGroupModal from "../modals/AgeGroupModal";
import DivisionModal from "../modals/DivisionModal";

interface TeamData {
  name: string;
  logo: string | null;
  ageGroup: string;
  division: string;
  completed: boolean;
}

interface Props {
  data: TeamData;
  onUpdate: (data: Partial<TeamData>) => void;
  canAccess: boolean;
}

const ageGroups = [
  "U8 (Under 8)",
  "U10 (Under 10)",
  "U12 (Under 12)",
  "U14 (Under 14)",
  "U16 (Under 16)",
  "U18 (Under 18)",
  "Adult (18+)",
  "Senior (35+)",
];

const divisionsByAge = {
  "U8 (Under 8)": ["Beginner", "Recreational"],
  "U10 (Under 10)": ["Beginner", "Recreational", "Competitive"],
  "U12 (Under 12)": ["Beginner", "Recreational", "Competitive"],
  "U14 (Under 14)": ["Recreational", "Competitive", "Elite"],
  "U16 (Under 16)": ["Recreational", "Competitive", "Elite"],
  "U18 (Under 18)": ["Recreational", "Competitive", "Elite"],
  "Adult (18+)": ["Recreational", "Competitive", "Elite", "Professional"],
  "Senior (35+)": ["Recreational", "Competitive"],
};

export default function TeamSection({ data, onUpdate, canAccess }: Props) {
  const [formData, setFormData] = useState({
    name: data.name,
    logo: data.logo,
    ageGroup: data.ageGroup,
    division: data.division,
  });

  const [errors, setErrors] = useState({
    name: "",
    logo: "",
    ageGroup: "",
    division: "",
  });
  const [ageGroupModalVisible, setAgeGroupModalVisible] = useState(false);
  const [divisionModalVisible, setDivisionModalVisible] = useState(false);

  useEffect(() => {
    validateAndUpdate();
  }, [formData]);

  const validateAndUpdate = () => {
    const newErrors = {
      name: "",
      logo: "",
      ageGroup: "",
      division: "",
    };

    // Validate team name
    if (!formData.name.trim()) {
      newErrors.name = "Team name is required";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Team name must be at least 2 characters";
    }

    // Validate logo
    if (!formData.logo) {
      newErrors.logo = "Team logo is required";
    }

    // Validate age group
    if (!formData.ageGroup) {
      newErrors.ageGroup = "Age group is required";
    }

    // Validate division
    if (!formData.division) {
      newErrors.division = "Division is required";
    }

    setErrors(newErrors);

    const isCompleted =
      formData.name.trim() !== "" &&
      formData.logo !== null &&
      formData.ageGroup !== "" &&
      formData.division !== "" &&
      !newErrors.name &&
      !newErrors.logo &&
      !newErrors.ageGroup &&
      !newErrors.division;

    onUpdate({
      ...formData,
      completed: isCompleted,
    });
  };

  const updateField = (field: keyof typeof formData, value: string | null) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const updateAgeGroup = (ageGroup: string) => {
    setFormData((prev) => ({
      ...prev,
      ageGroup,
      division: "", // Reset division when age group changes
    }));
  };

  const handleAgeGroupSelect = (ageGroup: string) => {
    updateAgeGroup(ageGroup);
    setAgeGroupModalVisible(false);
  };

  const handleDivisionSelect = (division: string) => {
    updateField("division", division);
    setDivisionModalVisible(false);
  };

  const getAvailableDivisions = () => {
    if (!formData.ageGroup) return [];
    return (
      divisionsByAge[formData.ageGroup as keyof typeof divisionsByAge] || []
    );
  };

  const pickImage = async () => {
    try {
      const permissionResult =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert(
          "Permission Required",
          "Permission to access camera roll is required!"
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        updateField("logo", result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert("Error", "Failed to pick image");
    }
  };

  if (!canAccess) {
    return (
      <View style={styles.lockedContainer}>
        <Ionicons name="lock-closed" size={48} color="#999" />
        <Text style={styles.lockedTitle}>Complete Previous Section</Text>
        <Text style={styles.lockedText}>
          Please complete the Tournament section before proceeding to Team
          information.
        </Text>
      </View>
    );
  }

  return (
    <>
      <ScrollView style={styles.container}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Team Information</Text>
            <Text style={styles.subtitle}>
              Provide your team details and upload a team logo
            </Text>
          </View>

          <View style={styles.form}>
            {/* Team Name */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>
                Team Name <Text style={styles.required}>*</Text>
              </Text>
              <TextInput
                style={[styles.input, errors.name && styles.inputError]}
                value={formData.name}
                onChangeText={(value) => updateField("name", value)}
                placeholder="Enter team name"
                placeholderTextColor="#999"
                autoCapitalize="words"
              />
              {errors.name ? (
                <Text style={styles.errorText}>{errors.name}</Text>
              ) : null}
            </View>

            {/* Team Logo */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>
                Team Logo <Text style={styles.required}>*</Text>
              </Text>
              <TouchableOpacity
                style={[styles.logoUpload, errors.logo && styles.inputError]}
                onPress={pickImage}
              >
                {formData.logo ? (
                  <Image
                    source={{ uri: formData.logo }}
                    style={styles.logoImage}
                  />
                ) : (
                  <View style={styles.logoPlaceholder}>
                    <Ionicons name="camera" size={32} color="#999" />
                    <Text style={styles.logoPlaceholderText}>
                      Tap to upload logo
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
              {errors.logo ? (
                <Text style={styles.errorText}>{errors.logo}</Text>
              ) : null}
            </View>

            {/* Age Group */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>
                Age Group <Text style={styles.required}>*</Text>
              </Text>
              <TouchableOpacity
                style={[styles.selector, errors.ageGroup && styles.inputError]}
                onPress={() => setAgeGroupModalVisible(true)}
              >
                <Text
                  style={[
                    styles.selectorText,
                    !formData.ageGroup && styles.placeholderText,
                  ]}
                >
                  {formData.ageGroup || "Select age group..."}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#666" />
              </TouchableOpacity>
              {errors.ageGroup ? (
                <Text style={styles.errorText}>{errors.ageGroup}</Text>
              ) : null}
            </View>

            {/* Division */}
            {formData.ageGroup && (
              <View style={styles.inputGroup}>
                <Text style={styles.label}>
                  Division <Text style={styles.required}>*</Text>
                </Text>
                <TouchableOpacity
                  style={[
                    styles.selector,
                    errors.division && styles.inputError,
                  ]}
                  onPress={() => setDivisionModalVisible(true)}
                >
                  <Text
                    style={[
                      styles.selectorText,
                      !formData.division && styles.placeholderText,
                    ]}
                  >
                    {formData.division || "Select division..."}
                  </Text>
                  <Ionicons name="chevron-down" size={20} color="#666" />
                </TouchableOpacity>
                {errors.division ? (
                  <Text style={styles.errorText}>{errors.division}</Text>
                ) : null}
              </View>
            )}
          </View>

          {/* Progress Indicator */}
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: data.completed ? "100%" : "0%" },
                ]}
              />
            </View>
            <Text style={styles.progressText}>
              {data.completed
                ? "✓ Section completed"
                : "Please complete all team information"}
            </Text>
          </View>
        </View>
      </ScrollView>

      <AgeGroupModal
        visible={ageGroupModalVisible}
        onClose={() => setAgeGroupModalVisible(false)}
        onSelect={handleAgeGroupSelect}
        selectedAgeGroup={formData.ageGroup}
      />

      <DivisionModal
        visible={divisionModalVisible}
        onClose={() => setDivisionModalVisible(false)}
        onSelect={handleDivisionSelect}
        selectedDivision={formData.division}
        ageGroup={formData.ageGroup}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  content: {
    padding: 16,
  },
  header: {
    alignItems: "flex-start",
    marginBottom: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#231716",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: "#555",
    textAlign: "left",
    lineHeight: 22,
  },
  form: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 1,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: "#231716",
    marginBottom: 8,
  },
  required: {
    color: "#D62828",
  },
  input: {
    borderWidth: 1,
    borderColor: "#E5E5E7",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: "#fff",
  },
  inputError: {
    borderColor: "#D62828",
  },
  errorText: {
    color: "#D62828",
    fontSize: 14,
    marginTop: 4,
  },
  selector: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: "#E5E5E7",
    borderRadius: 8,
    backgroundColor: "#fff",
    padding: 12,
    minHeight: 50,
  },
  selectorText: {
    fontSize: 16,
    color: "#1a1a1a",
  },
  placeholderText: {
    color: "#999",
  },
  logoUpload: {
    borderWidth: 2,
    borderColor: "#E5E5E7",
    borderStyle: "dashed",
    borderRadius: 8,
    height: 120,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F8F9FA",
  },
  logoImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  logoPlaceholder: {
    alignItems: "center",
  },
  logoPlaceholderText: {
    color: "#999",
    fontSize: 14,
    marginTop: 8,
  },
  progressContainer: {
    marginTop: 30,
    alignItems: "center",
  },
  progressBar: {
    width: "100%",
    height: 4,
    backgroundColor: "#E5E5E7",
    borderRadius: 2,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#4CAF50",
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: "#666",
    marginTop: 8,
    textAlign: "center",
  },
  lockedContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
    backgroundColor: "#F0F1F5",
  },
  lockedTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#999",
    marginTop: 16,
    marginBottom: 8,
  },
  lockedText: {
    fontSize: 16,
    color: "#999",
    textAlign: "center",
    lineHeight: 22,
  },
});
