import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
} from "react-native";
import { Stack, useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import GeneralSection from "./sections/GeneralSection";
import TournamentSection from "./sections/TournamentSection";
import TeamSection from "./sections/TeamSection";
import RosterSection from "./sections/RosterSection";

// Tab configuration
const tabs = [
  { id: "general", name: "General", component: GeneralSection },
  { id: "tournament", name: "Tournament", component: TournamentSection },
  { id: "team", name: "Team", component: TeamSection },
  { id: "roster", name: "Roster", component: RosterSection },
];

// Registration data interface
export interface RegistrationData {
  general: {
    representativeName: string;
    email: string;
    phone: string;
    completed: boolean;
  };
  tournament: {
    eventType: string;
    eventId: string;
    completed: boolean;
  };
  team: {
    name: string;
    logo: string | null;
    ageGroup: string;
    division: string;
    completed: boolean;
  };
  roster: {
    forwards: Array<{
      name: string;
      photo: string | null;
      jerseyNumber?: string;
    }>;
    defence: Array<{
      name: string;
      photo: string | null;
      jerseyNumber?: string;
    }>;
    goalies: Array<{
      name: string;
      photo: string | null;
      jerseyNumber?: string;
    }>;
    coaches: Array<{ name: string; photo: string | null; role?: string }>;
    completed: boolean;
  };
}

export default function TeamRegistrationScreen() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("general");
  const [registrationData, setRegistrationData] = useState<RegistrationData>({
    general: {
      representativeName: "",
      email: "",
      phone: "",
      completed: false,
    },
    tournament: {
      eventType: "",
      eventId: "",
      completed: false,
    },
    team: {
      name: "",
      logo: null,
      ageGroup: "",
      division: "",
      completed: false,
    },
    roster: {
      forwards: [],
      defence: [],
      goalies: [],
      coaches: [],
      completed: false,
    },
  });

  const updateRegistrationData = (
    section: keyof RegistrationData,
    data: any
  ) => {
    setRegistrationData((prev) => ({
      ...prev,
      [section]: { ...prev[section], ...data },
    }));
  };

  const canAccessTab = (tabId: string) => {
    switch (tabId) {
      case "general":
        return true;
      case "tournament":
        return registrationData.general.completed;
      case "team":
        return (
          registrationData.general.completed &&
          registrationData.tournament.completed
        );
      case "roster":
        return (
          registrationData.general.completed &&
          registrationData.tournament.completed &&
          registrationData.team.completed
        );
      default:
        return false;
    }
  };

  const renderTabContent = () => {
    const activeTabConfig = tabs.find((tab) => tab.id === activeTab);
    if (!activeTabConfig) return null;

    const Component = activeTabConfig.component;
    const sectionData = registrationData[activeTab as keyof RegistrationData];

    // Cast sectionData to the correct type for each section
    let typedSectionData: any = sectionData;
    switch (activeTab) {
      case "general":
        typedSectionData = sectionData as RegistrationData["general"];
        break;
      case "tournament":
        typedSectionData = sectionData as RegistrationData["tournament"];
        break;
      case "team":
        typedSectionData = sectionData as RegistrationData["team"];
        break;
      case "roster":
        typedSectionData = sectionData as RegistrationData["roster"];
        break;
      default:
        typedSectionData = sectionData;
    }

    return (
      <Component
        data={typedSectionData}
        allData={registrationData}
        onUpdate={(data: any) =>
          updateRegistrationData(activeTab as keyof RegistrationData, data)
        }
        canAccess={canAccessTab(activeTab)}
      />
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: "Team registration",
          headerLeft: () => (
            <TouchableOpacity
              onPress={() => router.back()}
              style={styles.backButton}
            >
              <Ionicons name="chevron-back" size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      <SafeAreaView style={styles.container}>
        {/* Custom Tab Bar */}
        <View style={styles.tabContainer}>
          <View style={styles.tabsWrapper}>
            {tabs.map((tab) => {
              const isActive = activeTab === tab.id;
              const canAccess = canAccessTab(tab.id);
              const isCompleted =
                registrationData[tab.id as keyof RegistrationData]?.completed;

              return (
                <TouchableOpacity
                  key={tab.id}
                  style={[styles.tab, !canAccess && styles.disabledTab]}
                  onPress={() => canAccess && setActiveTab(tab.id)}
                  disabled={!canAccess}
                  activeOpacity={0.8}
                >
                  <View style={styles.tabContent}>
                    <Text
                      style={[
                        styles.tabText,
                        isActive && styles.activeTabText,
                        !canAccess && styles.disabledTabText,
                      ]}
                    >
                      {tab.name}
                    </Text>
                  </View>
                  {isActive && <View style={styles.underline} />}
                </TouchableOpacity>
              );
            })}
          </View>
        </View>

        {/* Tab Content */}
        <View style={styles.contentContainer}>{renderTabContent()}</View>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  backButton: {
    padding: 8,
    marginLeft: -16,
  },
  tabContainer: {
    backgroundColor: "#fff",
    position: "relative",
    borderBottomWidth: 0.5,
    borderColor: "#999",
  },
  tabsWrapper: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  tab: {
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 18,
    paddingVertical: 12,
    position: "relative",
  },
  disabledTab: {
    opacity: 0.5,
  },
  tabContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  tabText: {
    fontSize: 16,
    fontWeight: "400",
    color: "#231716",
    textAlign: "center",
  },
  activeTabText: {
    fontWeight: "bold",
    color: "#231716",
  },
  disabledTabText: {
    color: "#888",
  },
  underline: {
    position: "absolute",
    bottom: 8,
    width: "100%",
    height: 2,
    backgroundColor: "#231716",
    borderRadius: 1,
    alignSelf: "center",
  },
  contentContainer: {
    flex: 1,
  },
});
