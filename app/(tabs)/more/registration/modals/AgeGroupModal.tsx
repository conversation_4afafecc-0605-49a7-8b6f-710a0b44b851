import React from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface AgeGroupModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (ageGroup: string) => void;
  selectedAgeGroup: string;
}

const ageGroups = [
  { id: "U8", name: "Under 8", description: "Ages 7 and under" },
  { id: "U10", name: "Under 10", description: "Ages 9 and under" },
  { id: "U12", name: "Under 12", description: "Ages 11 and under" },
  { id: "U14", name: "Under 14", description: "Ages 13 and under" },
  { id: "U16", name: "Under 16", description: "Ages 15 and under" },
  { id: "U18", name: "Under 18", description: "Ages 17 and under" },
  { id: "U20", name: "Under 20", description: "Ages 19 and under" },
  { id: "Senior", name: "Senior", description: "Ages 18 and over" },
];

export default function AgeGroupModal({
  visible,
  onClose,
  onSelect,
  selectedAgeGroup,
}: AgeGroupModalProps) {
  const handleSelect = (ageGroup: string) => {
    onSelect(ageGroup);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#1a1a1a" />
          </TouchableOpacity>
          <Text style={styles.title}>Select Age Group</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {ageGroups.map((ageGroup) => (
            <TouchableOpacity
              key={ageGroup.id}
              style={[
                styles.option,
                selectedAgeGroup === ageGroup.id && styles.selectedOption,
              ]}
              onPress={() => handleSelect(ageGroup.id)}
            >
              <View style={styles.optionContent}>
                <Text
                  style={[
                    styles.optionTitle,
                    selectedAgeGroup === ageGroup.id && styles.selectedOptionTitle,
                  ]}
                >
                  {ageGroup.name}
                </Text>
                <Text
                  style={[
                    styles.optionDescription,
                    selectedAgeGroup === ageGroup.id && styles.selectedOptionDescription,
                  ]}
                >
                  {ageGroup.description}
                </Text>
              </View>
              {selectedAgeGroup === ageGroup.id && (
                <Ionicons name="checkmark" size={24} color="#D62828" />
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E7",
  },
  closeButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: "transparent",
  },
  selectedOption: {
    borderColor: "#D62828",
    backgroundColor: "#FFF5F5",
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  selectedOptionTitle: {
    color: "#D62828",
  },
  optionDescription: {
    fontSize: 14,
    color: "#666",
  },
  selectedOptionDescription: {
    color: "#D62828",
  },
});
