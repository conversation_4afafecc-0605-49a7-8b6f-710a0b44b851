import React from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface DivisionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (division: string) => void;
  selectedDivision: string;
  ageGroup: string;
}

const getDivisionsForAgeGroup = (ageGroup: string) => {
  const divisionMap: { [key: string]: Array<{ id: string; name: string; description: string }> } = {
    "U8": [
      { id: "Recreational", name: "Recreational", description: "Fun-focused play" },
      { id: "Development", name: "Development", description: "Skill building focus" },
    ],
    "U10": [
      { id: "Recreational", name: "Recreational", description: "Fun-focused play" },
      { id: "Development", name: "Development", description: "Skill building focus" },
      { id: "Competitive", name: "Competitive", description: "Competitive play" },
    ],
    "U12": [
      { id: "Recreational", name: "Recreational", description: "Fun-focused play" },
      { id: "Development", name: "Development", description: "Skill building focus" },
      { id: "Competitive", name: "Competitive", description: "Competitive play" },
      { id: "Elite", name: "Elite", description: "High-level competitive play" },
    ],
    "U14": [
      { id: "Development", name: "Development", description: "Skill building focus" },
      { id: "Competitive", name: "Competitive", description: "Competitive play" },
      { id: "Elite", name: "Elite", description: "High-level competitive play" },
      { id: "Premier", name: "Premier", description: "Top tier competition" },
    ],
    "U16": [
      { id: "Competitive", name: "Competitive", description: "Competitive play" },
      { id: "Elite", name: "Elite", description: "High-level competitive play" },
      { id: "Premier", name: "Premier", description: "Top tier competition" },
    ],
    "U18": [
      { id: "Competitive", name: "Competitive", description: "Competitive play" },
      { id: "Elite", name: "Elite", description: "High-level competitive play" },
      { id: "Premier", name: "Premier", description: "Top tier competition" },
    ],
    "U20": [
      { id: "Elite", name: "Elite", description: "High-level competitive play" },
      { id: "Premier", name: "Premier", description: "Top tier competition" },
    ],
    "Senior": [
      { id: "Recreational", name: "Recreational", description: "Fun-focused play" },
      { id: "Competitive", name: "Competitive", description: "Competitive play" },
      { id: "Elite", name: "Elite", description: "High-level competitive play" },
    ],
  };

  return divisionMap[ageGroup] || [];
};

export default function DivisionModal({
  visible,
  onClose,
  onSelect,
  selectedDivision,
  ageGroup,
}: DivisionModalProps) {
  const divisions = getDivisionsForAgeGroup(ageGroup);

  const handleSelect = (division: string) => {
    onSelect(division);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#1a1a1a" />
          </TouchableOpacity>
          <Text style={styles.title}>Select Division</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {divisions.length > 0 ? (
            divisions.map((division) => (
              <TouchableOpacity
                key={division.id}
                style={[
                  styles.option,
                  selectedDivision === division.id && styles.selectedOption,
                ]}
                onPress={() => handleSelect(division.id)}
              >
                <View style={styles.optionContent}>
                  <Text
                    style={[
                      styles.optionTitle,
                      selectedDivision === division.id && styles.selectedOptionTitle,
                    ]}
                  >
                    {division.name}
                  </Text>
                  <Text
                    style={[
                      styles.optionDescription,
                      selectedDivision === division.id && styles.selectedOptionDescription,
                    ]}
                  >
                    {division.description}
                  </Text>
                </View>
                {selectedDivision === division.id && (
                  <Ionicons name="checkmark" size={24} color="#D62828" />
                )}
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyText}>
                Please select an age group first to see available divisions.
              </Text>
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E7",
  },
  closeButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: "transparent",
  },
  selectedOption: {
    borderColor: "#D62828",
    backgroundColor: "#FFF5F5",
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  selectedOptionTitle: {
    color: "#D62828",
  },
  optionDescription: {
    fontSize: 14,
    color: "#666",
  },
  selectedOptionDescription: {
    color: "#D62828",
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
});
