import React from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  SafeAreaView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface EventTypeModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (eventType: string) => void;
  selectedEventType: string;
}

const eventTypes = [
  { id: "tournament", name: "Tournament", description: "Competitive tournament play" },
  { id: "showcase", name: "Showcase", description: "Skills showcase event" },
  { id: "league", name: "League", description: "Regular season league play" },
];

export default function EventTypeModal({
  visible,
  onClose,
  onSelect,
  selectedEventType,
}: EventTypeModalProps) {
  const handleSelect = (eventType: string) => {
    onSelect(eventType);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#1a1a1a" />
          </TouchableOpacity>
          <Text style={styles.title}>Select Event Type</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.content}>
          {eventTypes.map((eventType) => (
            <TouchableOpacity
              key={eventType.id}
              style={[
                styles.option,
                selectedEventType === eventType.id && styles.selectedOption,
              ]}
              onPress={() => handleSelect(eventType.id)}
            >
              <View style={styles.optionContent}>
                <Text
                  style={[
                    styles.optionTitle,
                    selectedEventType === eventType.id && styles.selectedOptionTitle,
                  ]}
                >
                  {eventType.name}
                </Text>
                <Text
                  style={[
                    styles.optionDescription,
                    selectedEventType === eventType.id && styles.selectedOptionDescription,
                  ]}
                >
                  {eventType.description}
                </Text>
              </View>
              {selectedEventType === eventType.id && (
                <Ionicons name="checkmark" size={24} color="#D62828" />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E7",
  },
  closeButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: "transparent",
  },
  selectedOption: {
    borderColor: "#D62828",
    backgroundColor: "#FFF5F5",
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  selectedOptionTitle: {
    color: "#D62828",
  },
  optionDescription: {
    fontSize: 14,
    color: "#666",
  },
  selectedOptionDescription: {
    color: "#D62828",
  },
});
