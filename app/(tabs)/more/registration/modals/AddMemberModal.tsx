import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Image,
  Alert,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";

export interface MemberData {
  name: string;
  photo: string | null;
  jerseyNumber?: string;
  role?: string;
}

interface AddMemberModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (member: MemberData) => void;
  position: "forwards" | "defence" | "goalies" | "coaches";
  editingMember?: MemberData | null;
  isEditing?: boolean;
}

export default function AddMemberModal({
  visible,
  onClose,
  onSave,
  position,
  editingMember,
  isEditing = false,
}: AddMemberModalProps) {
  const [formData, setFormData] = useState<MemberData>({
    name: "",
    photo: null,
    jerseyNumber: "",
    role: "",
  });

  useEffect(() => {
    if (editingMember) {
      setFormData(editingMember);
    } else {
      setFormData({
        name: "",
        photo: null,
        jerseyNumber: "",
        role: "",
      });
    }
  }, [editingMember, visible]);

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Please grant camera roll permissions to upload photos."
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setFormData((prev) => ({ ...prev, photo: result.assets[0].uri }));
      }
    } catch (error) {
      Alert.alert("Error", "Failed to pick image. Please try again.");
    }
  };

  const handleSave = () => {
    if (!formData.name.trim()) {
      Alert.alert("Error", "Please enter a name.");
      return;
    }

    if (!formData.photo) {
      Alert.alert("Error", "Please add a photo.");
      return;
    }

    onSave(formData);
    onClose();
  };

  const handleClose = () => {
    setFormData({
      name: "",
      photo: null,
      jerseyNumber: "",
      role: "",
    });
    onClose();
  };

  const isCoach = position === "coaches";
  const title = `${isEditing ? "Edit" : "Add"} ${isCoach ? "Coach" : "Player"}`;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#1a1a1a" />
          </TouchableOpacity>
          <Text style={styles.title}>{title}</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Photo Upload */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>
              Photo <Text style={styles.required}>*</Text>
            </Text>
            <TouchableOpacity style={styles.photoUpload} onPress={pickImage}>
              {formData.photo ? (
                <Image source={{ uri: formData.photo }} style={styles.photoImage} />
              ) : (
                <View style={styles.photoPlaceholder}>
                  <Ionicons name="camera" size={32} color="#999" />
                  <Text style={styles.photoPlaceholderText}>Tap to add photo</Text>
                </View>
              )}
            </TouchableOpacity>
          </View>

          {/* Name */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>
              Full Name <Text style={styles.required}>*</Text>
            </Text>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(value) =>
                setFormData((prev) => ({ ...prev, name: value }))
              }
              placeholder="Enter full name"
              placeholderTextColor="#999"
              autoCapitalize="words"
            />
          </View>

          {/* Jersey Number (for players) */}
          {!isCoach && (
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Jersey Number (Optional)</Text>
              <TextInput
                style={styles.input}
                value={formData.jerseyNumber}
                onChangeText={(value) =>
                  setFormData((prev) => ({ ...prev, jerseyNumber: value }))
                }
                placeholder="Enter jersey number"
                placeholderTextColor="#999"
                keyboardType="numeric"
              />
            </View>
          )}

          {/* Role (for coaches) */}
          {isCoach && (
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Role (Optional)</Text>
              <TextInput
                style={styles.input}
                value={formData.role}
                onChangeText={(value) =>
                  setFormData((prev) => ({ ...prev, role: value }))
                }
                placeholder="e.g., Head Coach, Assistant Coach"
                placeholderTextColor="#999"
                autoCapitalize="words"
              />
            </View>
          )}

          <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
            <Text style={styles.saveButtonText}>
              {isEditing ? "Update" : "Add"} Member
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E7",
  },
  closeButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 8,
  },
  required: {
    color: "#D62828",
  },
  input: {
    borderWidth: 1,
    borderColor: "#E5E5E7",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: "#fff",
  },
  photoUpload: {
    borderWidth: 2,
    borderColor: "#E5E5E7",
    borderStyle: "dashed",
    borderRadius: 8,
    height: 120,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F8F9FA",
  },
  photoImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  photoPlaceholder: {
    alignItems: "center",
  },
  photoPlaceholderText: {
    color: "#999",
    fontSize: 14,
    marginTop: 8,
  },
  saveButton: {
    backgroundColor: "#D62828",
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
    marginTop: 20,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
