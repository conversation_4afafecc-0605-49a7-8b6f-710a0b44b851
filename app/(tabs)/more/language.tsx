import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

export default function LanguageScreen() {
  const router = useRouter();
  const [selectedLanguage, setSelectedLanguage] = useState<"en" | "zh">("en");

  const handleLanguageSelection = (language: "en" | "zh") => {
    setSelectedLanguage(language);
    // Here you would typically persist the language preference
    // and apply it to the app
    // For now, we'll just update the state and go back
    setTimeout(() => {
      router.back();
    }, 300);
  };

  const languages = [
    { key: "en" as const, label: "English", nativeLabel: "English" },
    { key: "zh" as const, label: "Chinese", nativeLabel: "中文" },
  ];

  return (
    <SafeAreaView style={styles.container}>
      {/* Custom Header with Exit Button */}
      <View style={styles.headerContainer}>
        <View style={styles.headerSpacer} />
        <Text style={styles.headerTitle}>Language</Text>
        <TouchableOpacity
          style={styles.exitButton}
          onPress={() => router.back()}
        >
          <View style={styles.roundXButton}>
            <Ionicons name="close" size={17} color="#000" />
          </View>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContent}>
        <View style={styles.contentContainer}>
          <View style={styles.contentSection}>
            <Text style={styles.sectionDescription}>
              Select your preferred language for the app interface.
            </Text>
            
            {languages.map((language) => (
              <TouchableOpacity
                key={language.key}
                style={[
                  styles.languageOption,
                  selectedLanguage === language.key && styles.languageOptionSelected
                ]}
                onPress={() => handleLanguageSelection(language.key)}
              >
                <View style={styles.languageOptionContent}>
                  <Text style={[
                    styles.languageOptionTitle,
                    selectedLanguage === language.key && styles.languageOptionTitleSelected
                  ]}>
                    {language.nativeLabel}
                  </Text>
                  <Text style={styles.languageOptionSubtitle}>
                    {language.label}
                  </Text>
                </View>
                {selectedLanguage === language.key && (
                  <Ionicons name="checkmark" size={20} color="#346eca" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 12,
    paddingTop: 10,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    backgroundColor: "#fff",
  },
  headerSpacer: {
    width: 28,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000",
    flex: 1,
    textAlign: "center",
  },
  exitButton: {
    paddingHorizontal: 8,
    paddingVertical: 5,
  },
  roundXButton: {
    width: 25,
    height: 25,
    borderRadius: 14,
    backgroundColor: "#EEEEEE",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  scrollContent: {
    flex: 1,
  },
  contentContainer: {
    backgroundColor: "#FFFFFF",
    marginHorizontal: 10,
    marginTop: 15,
    borderRadius: 7,
    shadowColor: "#000",
    shadowOpacity: 0.07,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 1 },
    elevation: 1,
  },
  contentSection: {
    padding: 15,
  },
  sectionDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 20,
    textAlign: "center",
  },
  languageOption: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginVertical: 4,
    borderWidth: 1,
    borderColor: "#E5E5E7",
  },
  languageOptionSelected: {
    backgroundColor: "#F0F8FF",
    borderColor: "#346eca",
  },
  languageOptionContent: {
    flex: 1,
  },
  languageOptionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "black",
    marginBottom: 2,
  },
  languageOptionTitleSelected: {
    color: "#346eca",
  },
  languageOptionSubtitle: {
    fontSize: 14,
    color: "#666",
  },
});
