import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Alert,
} from "react-native";
import { useRouter } from "expo-router";
import Header from "./components/Header";
import LeagueTabs from "./components/LeagueTabs";
import StandingsTab from "./components/StandingsTab";
import LeagueSelectionModal from "./components/LeagueSelectionModal";
import { leagues, getLeagueById, League } from "./../../../data/leagues/leagues";
import GamesTab from "./components/GamesTab";
import TeamsTab from "./components/TeamsTab";
import NewsTab from "./components/NewsTab";

const tabs = ["Standings", "Games", "Teams", "News"];

function StandingsContent({ selectedLeague }: { selectedLeague: League }) {
  return <StandingsTab />;
}

function GamesContent({ selectedLeague }: { selectedLeague: League }) {
  return <GamesTab />;
}

function TeamsContent({ selectedLeague }: { selectedLeague: League }) {
  return <TeamsTab />;
}

function NewsContent({ selectedLeague }: { selectedLeague: League }) {
  return <NewsTab />;
}

export default function LeaguesScreen() {
  const [activeTab, setActiveTab] = useState("Standings");
  const [selectedLeagueId, setSelectedLeagueId] = useState("hkphl");
  const [isModalVisible, setIsModalVisible] = useState(false);

  const selectedLeague = getLeagueById(selectedLeagueId) || leagues[0];

  const handleHKPHLPress = () => {
    setIsModalVisible(true);
  };

  const handleLeagueSelect = (league: League) => {
    setSelectedLeagueId(league.id);
    setIsModalVisible(false);
  };

  const handleTabPress = (tab: string) => {
    setActiveTab(tab);
  };

  const renderContent = () => {
    switch (activeTab) {
      case "Standings":
        return <StandingsContent selectedLeague={selectedLeague} />;
      case "Games":
        return <GamesContent selectedLeague={selectedLeague} />;
      case "Teams":
        return <TeamsContent selectedLeague={selectedLeague} />;
      case "News":
        return <NewsContent selectedLeague={selectedLeague} />;
      default:
        return <StandingsContent selectedLeague={selectedLeague} />;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Leagues"
        onHKPHLPress={handleHKPHLPress}
        leagueName={selectedLeague.name}
        leagueFullName={selectedLeague.fullName}
      />

      <LeagueTabs
        tabs={tabs}
        selectedTab={activeTab}
        onTabSelect={handleTabPress}
      />

      {renderContent()}

      <LeagueSelectionModal
        visible={isModalVisible}
        leagues={leagues}
        selectedLeagueId={selectedLeagueId}
        onLeagueSelect={handleLeagueSelect}
        onClose={() => setIsModalVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  contentContainer: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  tabTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginBottom: 12,
  },
  placeholder: {
    fontSize: 16,
    color: "#8E8E93",
    textAlign: "center",
  },
});
