import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  SafeAreaView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { League } from "../../../../data/leagues/leagues";

interface LeagueSelectionModalProps {
  visible: boolean;
  leagues: League[];
  selectedLeagueId: string;
  onLeagueSelect: (league: League) => void;
  onClose: () => void;
}

export default function LeagueSelectionModal({
  visible,
  leagues,
  selectedLeagueId,
  onLeagueSelect,
  onClose,
}: LeagueSelectionModalProps) {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Custom Header with Exit Button */}
        <View style={styles.header}>
          <View style={styles.headerSpacer} />
          <Text style={styles.title}>Select League</Text>
          <TouchableOpacity style={styles.exitButton} onPress={onClose}>
            <View style={styles.roundXButton}>
              <Ionicons name="close" size={17} color="#000" />
            </View>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.scrollView}>
          {leagues.map((league) => (
            <TouchableOpacity
              key={league.id}
              style={[
                styles.leagueItem,
                selectedLeagueId === league.id && styles.selectedLeagueItem,
              ]}
              onPress={() => onLeagueSelect(league)}
              activeOpacity={0.7}
            >
              <View style={styles.leagueContent}>
                <View style={styles.leagueInfo}>
                  <Text style={styles.leagueName}>{league.name}</Text>
                  <Text style={styles.leagueFullName}>{league.fullName}</Text>
                  <Text style={styles.leagueDescription}>
                    {league.description}
                  </Text>
                </View>
                {selectedLeagueId === league.id && (
                  <Ionicons name="checkmark" size={24} color="#D52B1E" />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 12,
    paddingTop: 10,
    paddingBottom: 5,
    backgroundColor: "#F0F1F5",
  },
  headerSpacer: {
    width: 28,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#000",
    flex: 1,
    textAlign: "center",
  },
  exitButton: {
    paddingHorizontal: 8,
    paddingVertical: 5,
  },
  roundXButton: {
    width: 25,
    height: 25,
    borderRadius: 14,
    backgroundColor: "#EEEEEE",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 10,
    paddingTop: 16,
  },
  leagueItem: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    marginBottom: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedLeagueItem: {
    borderWidth: 2,
    borderColor: "#D52B1E",
  },
  leagueContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  leagueInfo: {
    flex: 1,
    marginRight: 12,
  },
  leagueName: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#D52B1E",
    marginBottom: 4,
  },
  leagueFullName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  leagueDescription: {
    fontSize: 14,
    color: "#8E8E93",
    lineHeight: 20,
  },
});
