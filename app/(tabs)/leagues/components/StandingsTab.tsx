import React, { useState } from "react";
import { View, Text, StyleSheet, ScrollView, Image } from "react-native";
import { easternStandings } from "../../../../data/leagues/standings"; // <-- fixed import

function ShieldIcon() {
  return (
    <View style={styles.shieldContainer}>
      <View style={styles.shieldShape} />
    </View>
  );
}

function TeamLogoOrShield({ logo }: { logo?: string }) {
  const [error, setError] = useState(false);
  if (!logo || logo.trim() === "" || error) {
    return <ShieldIcon />;
  }
  return (
    <Image
      source={{ uri: logo }}
      style={styles.teamLogo}
      onError={() => setError(true)}
    />
  );
}

export default function StandingsTab() {
  // Define column widths for perfect alignment
  const COL_WIDTHS = {
    rank: 30,
    team: 60,
    gp: 40,
    w: 40,
    l: 40,
    t: 40,
    pts: 40,
    gf: 55,
    ga: 55,
    gd: 55,
    regw: 55,
    otw: 55,
    otl: 55,
  };

  return (
    <ScrollView style={styles.tabContainer}>
      <ScrollView horizontal showsHorizontalScrollIndicator={true}>
        <View>
          <View style={styles.tableHeaderRow}>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.rank, maxWidth: COL_WIDTHS.rank },
              ]}
            >
              #
            </Text>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.team, maxWidth: COL_WIDTHS.team },
              ]}
            >
              Team
            </Text>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.gp, maxWidth: COL_WIDTHS.gp },
              ]}
            >
              GP
            </Text>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.w, maxWidth: COL_WIDTHS.w },
              ]}
            >
              W
            </Text>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.l, maxWidth: COL_WIDTHS.l },
              ]}
            >
              L
            </Text>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.t, maxWidth: COL_WIDTHS.t },
              ]}
            >
              T
            </Text>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.pts, maxWidth: COL_WIDTHS.pts },
              ]}
            >
              PTS
            </Text>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.gf, maxWidth: COL_WIDTHS.gf },
              ]}
            >
              GF
            </Text>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.ga, maxWidth: COL_WIDTHS.ga },
              ]}
            >
              GA
            </Text>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.gd, maxWidth: COL_WIDTHS.gd },
              ]}
            >
              GD
            </Text>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.regw, maxWidth: COL_WIDTHS.regw },
              ]}
            >
              RegW
            </Text>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.otw, maxWidth: COL_WIDTHS.otw },
              ]}
            >
              OTW
            </Text>
            <Text
              style={[
                styles.headerCell,
                { minWidth: COL_WIDTHS.otl, maxWidth: COL_WIDTHS.otl },
              ]}
            >
              OTL
            </Text>
          </View>
          {easternStandings.map((team: any, idx: number) => {
            // <-- fixed types
            const isGrey = idx % 2 === 0;
            return (
              <View
                key={team.rank}
                style={[
                  styles.tableRow,
                  isGrey ? styles.greyRow : styles.whiteRow,
                ]}
              >
                <Text
                  style={[
                    styles.cell,
                    { minWidth: COL_WIDTHS.rank, maxWidth: COL_WIDTHS.rank },
                  ]}
                >
                  {team.rank}
                </Text>
                <View
                  style={[
                    styles.cell,
                    {
                      minWidth: COL_WIDTHS.team,
                      maxWidth: COL_WIDTHS.team,
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                    },
                  ]}
                >
                  <TeamLogoOrShield logo={team.logo} />
                  <Text style={{ marginLeft: 4, fontWeight: "bold" }}>
                    {team.team}
                  </Text>
                </View>
                <Text
                  style={[
                    styles.cell,
                    { minWidth: COL_WIDTHS.gp, maxWidth: COL_WIDTHS.gp },
                  ]}
                >
                  {team.gamesPlayed}
                </Text>
                <Text
                  style={[
                    styles.cell,
                    { minWidth: COL_WIDTHS.w, maxWidth: COL_WIDTHS.w },
                  ]}
                >
                  {team.wins}
                </Text>
                <Text
                  style={[
                    styles.cell,
                    { minWidth: COL_WIDTHS.l, maxWidth: COL_WIDTHS.l },
                  ]}
                >
                  {team.losses}
                </Text>
                <Text
                  style={[
                    styles.cell,
                    { minWidth: COL_WIDTHS.t, maxWidth: COL_WIDTHS.t },
                  ]}
                >
                  {team.ties}
                </Text>
                <Text
                  style={[
                    styles.cell,
                    {
                      minWidth: COL_WIDTHS.pts,
                      maxWidth: COL_WIDTHS.pts,
                      fontWeight: "bold",
                    },
                  ]}
                >
                  {team.points}
                </Text>
                <Text
                  style={[
                    styles.cell,
                    { minWidth: COL_WIDTHS.gf, maxWidth: COL_WIDTHS.gf },
                  ]}
                >
                  {team.goalsFor}
                </Text>
                <Text
                  style={[
                    styles.cell,
                    { minWidth: COL_WIDTHS.ga, maxWidth: COL_WIDTHS.ga },
                  ]}
                >
                  {team.goalsAgainst}
                </Text>
                <Text
                  style={[
                    styles.cell,
                    { minWidth: COL_WIDTHS.gd, maxWidth: COL_WIDTHS.gd },
                  ]}
                >
                  {team.goalDifference}
                </Text>
                <Text
                  style={[
                    styles.cell,
                    { minWidth: COL_WIDTHS.regw, maxWidth: COL_WIDTHS.regw },
                  ]}
                >
                  {team.regulationWins}
                </Text>
                <Text
                  style={[
                    styles.cell,
                    { minWidth: COL_WIDTHS.otw, maxWidth: COL_WIDTHS.otw },
                  ]}
                >
                  {team.overtimeWins}
                </Text>
                <Text
                  style={[
                    styles.cell,
                    { minWidth: COL_WIDTHS.otl, maxWidth: COL_WIDTHS.otl },
                  ]}
                >
                  {team.overtimeLosses}
                </Text>
              </View>
            );
          })}
        </View>
      </ScrollView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  tabContainer: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  tableHeaderRow: {
    flexDirection: "row",
    alignItems: "center",
    borderTopWidth: 1,
    borderColor: "#e0e0e0",
    fontWeight: "bold",
    backgroundColor: "#fff",
    paddingVertical: 8,
    paddingHorizontal: 5,
  },
  headerCell: {
    fontWeight: "bold",
    fontSize: 15,
    color: "#333",
    textAlign: "center",
    textAlignVertical: "center",
  },
  tableRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    paddingHorizontal: 5,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  whiteRow: {
    backgroundColor: "#fff",
  },
  greyRow: {
    backgroundColor: "#f7f7f7",
  },
  cell: {
    fontSize: 15,
    color: "#333",
    textAlign: "center",
    paddingHorizontal: 0,
    textAlignVertical: "center",
    justifyContent: "center",
    alignItems: "center",
  },
  teamLogo: {
    width: 22,
    height: 22,
    resizeMode: "contain",
    marginRight: 2,
  },
  shieldContainer: {
    width: 22,
    height: 22,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 2,
  },
  shieldShape: {
    width: 18,
    height: 20,
    backgroundColor: "#bdbdbd",
    borderTopLeftRadius: 9,
    borderTopRightRadius: 9,
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4,
  },
});
