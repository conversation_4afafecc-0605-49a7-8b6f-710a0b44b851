import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useFonts, BakbakOne_400Regular } from "@expo-google-fonts/bakbak-one";

interface HeaderProps {
  title: string;
  onFilterPress?: () => void;
  showFilter?: boolean;
  onHKPHLPress?: () => void;
  leagueName?: string;
  leagueFullName?: string;
}

export default function Header({
  title,
  onFilterPress,
  showFilter = false,
  onHKPHLPress,
  leagueName = "HKPHL",
  leagueFullName = "Hong Kong Premier Hockey League",
}: HeaderProps) {
  const [fontsLoaded] = useFonts({
    BakbakOne_400Regular,
  });

  if (!fontsLoaded) {
    return null;
  }

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="#D52B1E" />
      <View style={styles.container}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.hkphlButton}
            onPress={onHKPHLPress}
            activeOpacity={0.7}
          >
            <Text style={styles.hkphlText}>{leagueName}</Text>
            <Ionicons name="chevron-down" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
        <Text style={styles.fullName}>{leagueFullName}</Text>
        {showFilter && (
          <TouchableOpacity
            style={styles.filterButton}
            onPress={onFilterPress}
            activeOpacity={0.7}
          >
            <Ionicons name="filter" size={26} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    backgroundColor: "#D52B1E",
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    marginBottom: 8,
  },
  hkphlButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#D52B1E",
    borderWidth: 1,
    borderColor: "#FFFFFF",
    borderRadius: 10,
    paddingHorizontal: 10,
    gap: 5,
  },
  hkphlText: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#FFFFFF",
    fontFamily: "BakbakOne_400Regular",
  },
  fullName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#FFFFFF",
    fontFamily: "BakbakOne_400Regular",
    textAlign: "left",
  },
  filterButton: {
    position: "absolute",
    right: 20,
    top: 10,
  },
});
