import React, { useState } from "react";
import { View, Text, StyleSheet, ScrollView, Image } from "react-native";
import { games, Team, Game } from "../../../../data/leagues/games"; // <-- Use the correct data file

function ShieldIcon() {
  return (
    <View style={styles.shieldContainer}>
      <View style={styles.shieldShape} />
    </View>
  );
}

function TeamLogoOrShield({ logo }: { logo?: string }) {
  const [error, setError] = useState(false);
  if (!logo || logo.trim() === "" || error) {
    return <ShieldIcon />;
  }
  return (
    <Image
      source={{ uri: logo }}
      style={styles.teamLogo}
      onError={() => setError(true)}
    />
  );
}

function ScheduleCard({ game }: { game: Game }) {
  const [datePart, yearPart] = (() => {
    const parts = game.date.split(", ");
    if (parts.length === 2) {
      const yearMatch = parts[1].match(/\d{4}$/);
      const year = yearMatch ? yearMatch[0] : "";
      return [parts[0] + ", " + parts[1].replace(/\s*\d{4}$/, ""), year];
    }
    const match = game.date.match(/(.*?)(\d{4})$/);
    if (match) {
      return [match[1].trim(), match[2]];
    }
    return [game.date, ""];
  })();

  return (
    <View style={styles.card}>
      <Text style={styles.gameNumber}>Game {game.gameNumber}</Text>
      <View style={styles.scoresRow}>
        <View style={styles.teamsScoresContainer}>
          {game.teams.map((team: Team, idx: number) => (
            <View key={team.name} style={styles.teamRow}>
              <View style={styles.teamInfo}>
                <TeamLogoOrShield logo={team.logo} />
                <Text style={styles.teamName}>{team.name}</Text>
              </View>
              {team.score !== null && (
                <Text style={styles.teamScore}>{team.score}</Text>
              )}
            </View>
          ))}
        </View>
        <View style={styles.verticalSeparator} />
        <View style={styles.dateContainer}>
          <Text style={styles.gameDate}>{datePart}</Text>
          <Text style={styles.gameYear}>{yearPart}</Text>
        </View>
      </View>
    </View>
  );
}

export default function ScheduleTab() {
  return (
    <ScrollView style={styles.tabContainer}>
      {games.map((game) => (
        <ScheduleCard key={game.gameNumber} game={game} />
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  tabContainer: {
    flex: 1,
    backgroundColor: "#F0F1F5",
    paddingTop: 15,
  },
  card: {
    backgroundColor: "#fff",
    borderRadius: 7,
    marginHorizontal: 10,
    marginBottom: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
    display: "flex",
    flexDirection: "column",
    gap: 5,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  gameNumber: {
    fontSize: 14,
    color: "#7F7271",
    fontWeight: "400",
    marginTop: 5,
    marginBottom: 2,
  },
  scoresRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  teamsScoresContainer: {
    flex: 1,
    flexDirection: "column",
    gap: 6,
  },
  verticalSeparator: {
    width: 1,
    height: 70,
    backgroundColor: "#ab9f9e",
    marginHorizontal: 15,
    alignSelf: "center",
  },
  dateContainer: {
    justifyContent: "center",
    alignItems: "center",
    flexWrap: "wrap",
    flex: 0,
  },
  teamRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 2,
    gap: 1,
  },
  teamInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  teamLogo: {
    width: 32,
    height: 32,
    resizeMode: "contain",
    marginRight: 0,
  },
  shieldContainer: {
    width: 32,
    height: 32,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 0,
  },
  shieldShape: {
    width: 26,
    height: 28,
    backgroundColor: "#bdbdbd",
    borderTopLeftRadius: 13,
    borderTopRightRadius: 13,
    borderBottomLeftRadius: 6,
    borderBottomRightRadius: 6,
  },
  teamName: {
    fontWeight: "400",
    fontSize: 20,
    color: "#231716",
  },
  teamScore: {
    fontWeight: "400",
    fontSize: 20,
    color: "#231716",
  },
  gameDate: {
    fontSize: 15,
    color: "#231716",
    textAlign: "right",
    lineHeight: 18,
  },
  gameYear: {
    fontSize: 15,
    color: "#231716",
    textAlign: "right",
    lineHeight: 18,
    marginTop: 0,
  },
});
