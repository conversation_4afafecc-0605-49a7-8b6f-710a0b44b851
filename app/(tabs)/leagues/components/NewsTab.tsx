import React from "react";
import { View, Text, StyleSheet, ScrollView, Image } from "react-native";
import { leagueNews } from "../../../../data/leagues/news";

export default function NewsTab() {
  return (
    <ScrollView style={styles.tabContainer}>
      {leagueNews.map((item) => (
        <View key={item.id} style={styles.card}>
          {item.image && (
            <Image source={{ uri: item.image }} style={styles.image} />
          )}
          <View style={styles.content}>
            <Text style={styles.title}>{item.title}</Text>
            <Text style={styles.date}>{item.date}</Text>
            <Text style={styles.summary}>{item.summary}</Text>
          </View>
        </View>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  tabContainer: {
    flex: 1,
    backgroundColor: "#F0F1F5",
    paddingTop: 15,
  },
  card: {
    backgroundColor: "#fff",
    borderRadius: 8,
    marginHorizontal: 10,
    marginBottom: 12,
    padding: 16,
    flexDirection: "row",
    elevation: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 14,
    backgroundColor: "#eee",
  },
  content: {
    flex: 1,
    justifyContent: "center",
  },
  title: {
    fontSize: 17,
    fontWeight: "bold",
    color: "#231716",
    marginBottom: 4,
  },
  date: {
    fontSize: 13,
    color: "#888",
    marginBottom: 6,
  },
  summary: {
    fontSize: 15,
    color: "#333",
  },
});
