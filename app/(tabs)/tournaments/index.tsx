import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  RefreshControl,
  Text,
} from "react-native";
import Header from "./components/Header";
import CountryTabs from "./components/CountryTabs";
import TournamentCard from "./components/TournamentCard";
import AddTournamentModal from "./components/AddTournamentModal";
import {
  fetchTournaments,
  countries,
  TournamentDetails,
} from "./../../../data/tournaments/tournaments";

export default function TournamentsScreen() {
  const [selectedCountry, setSelectedCountry] = useState("All");
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [editingTournament, setEditingTournament] =
    useState<TournamentDetails | null>(null);
  const [tournaments, setTournaments] = useState<TournamentDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTournaments();
  }, []);

  const loadTournaments = async () => {
    setLoading(true);
    setError(null);
    try {
      const fetchedTournaments = await fetchTournaments();
      console.log("Fetched tournaments count:", fetchedTournaments.length);
      console.log(
        "First tournament structure:",
        JSON.stringify(fetchedTournaments[0], null, 2)
      );
      setTournaments(fetchedTournaments);
    } catch (error) {
      console.error("Error loading tournaments:", error);
      setError("Failed to load tournaments");
      Alert.alert("Error", "Failed to load tournaments");
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTournaments();
    setRefreshing(false);
  };

  const filteredTournaments = tournaments.filter(
    (tournament) =>
      selectedCountry === "All" || tournament.country === selectedCountry
  );

  console.log("Filtered tournaments:", filteredTournaments); // Debug log

  const handleFilterPress = () => {
    Alert.alert(
      "Filter Options",
      "Advanced filtering options will be available here",
      [{ text: "OK", style: "default" }]
    );
  };

  const handleAddTournament = () => {
    setEditingTournament(null);
    setIsAddModalVisible(true);
  };

  const handleEditTournament = (tournament: TournamentDetails) => {
    setEditingTournament(tournament);
    setIsAddModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsAddModalVisible(false);
    setEditingTournament(null);
    // Refresh tournaments after adding/editing
    loadTournaments();
  };

  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator size="large" color="#D52B1E" style={styles.loader} />
      );
    }

    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Text style={styles.retryText} onPress={loadTournaments}>
            Tap to retry
          </Text>
        </View>
      );
    }

    if (filteredTournaments.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            {selectedCountry === "All"
              ? "No tournaments found"
              : `No tournaments found in ${selectedCountry}`}
          </Text>
        </View>
      );
    }

    return filteredTournaments.map((tournament) => {
      // Debug log for each tournament
      console.log("Rendering tournament:", {
        id: tournament.id,
        title: tournament.title,
        subtitle: tournament.subtitle,
        date: tournament.date,
        hasTitle: !!tournament.title,
        hasSubtitle: !!tournament.subtitle,
        hasDate: !!tournament.date,
      });

      return (
        <View key={tournament.id} style={styles.tournamentWrapper}>
          <TournamentCard
            tournament={tournament}
            onEdit={handleEditTournament}
          />
        </View>
      );
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Tournaments"
        onFilterPress={handleFilterPress}
        onAddPress={handleAddTournament}
        showFilter={true}
        showAdd={true}
      />

      <CountryTabs
        countries={countries}
        selectedCountry={selectedCountry}
        onCountrySelect={setSelectedCountry}
      />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderContent()}
      </ScrollView>

      <AddTournamentModal
        visible={isAddModalVisible}
        onClose={handleCloseModal}
        editingTournament={editingTournament}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 10,
    paddingVertical: 16,
  },
  loader: {
    marginTop: 50,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 50,
  },
  errorText: {
    fontSize: 16,
    color: "#D52B1E",
    textAlign: "center",
    marginBottom: 10,
  },
  retryText: {
    fontSize: 14,
    color: "#007AFF",
    textAlign: "center",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
  tournamentWrapper: {
    marginBottom: 12,
  },
});
