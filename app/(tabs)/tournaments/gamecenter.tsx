import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  SafeAreaView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";

// Game data matching the design specification
const game = {
  gameNumber: 1,
  date: "Tu<PERSON>, 3 Jun",
  teams: [
    {
      name: "Panthers",
      fullName: "Florida Panthers",
      logo: "florida_panthers_logo.png",
      score: 4,
      periodScores: [1, 1, 1, 1],
    },
    {
      name: "<PERSON>",
      fullName: "Edmonton Oilers",
      logo: "edmonton_oilers_logo.png",
      score: 3,
      periodScores: [2, 1, 0, 0],
    },
  ],
};

// Roster data matching the design specification
const rosterData = {
  Oilers: [
    { number: 10, name: "<PERSON><PERSON> <PERSON>", position: "C", gp: null, g: null, a: null },
    { number: 13, name: "<PERSON><PERSON>", position: "C", gp: 18, g: 3, a: 1 },
    { number: 18, name: "<PERSON><PERSON>", position: "<PERSON><PERSON>", gp: 15, g: 5, a: 6 },
    { number: 19, name: "<PERSON><PERSON>", position: "<PERSON>", gp: 18, g: 4, a: 2 },
    { number: 21, name: "T. <PERSON>", position: "C", gp: 18, g: 1, a: 3 },
    {
      number: 22,
      name: "M. Savoie",
      position: "C",
      gp: null,
      g: null,
      a: null,
    },
    { number: 28, name: "C. <PERSON>", position: "RW", gp: 16, g: 5, a: 3 },
  ],
  <PERSON>: [
    { number: 16, name: "A. Barkov", position: "C", gp: 18, g: 8, a: 12 },
    { number: 19, name: "M. Tkachuk", position: "LW", gp: 18, g: 6, a: 9 },
    { number: 23, name: "C. Reinhart", position: "C", gp: 18, g: 4, a: 8 },
    { number: 27, name: "E. Rodrigues", position: "C", gp: 15, g: 3, a: 5 },
    { number: 70, name: "A. Lundell", position: "C", gp: 18, g: 2, a: 6 },
    { number: 13, name: "S. Bennett", position: "C", gp: 16, g: 5, a: 3 },
    { number: 17, name: "N. Cousins", position: "C", gp: 12, g: 1, a: 2 },
  ],
};

// Segmented Control Component
function SegmentedControl({ options, selectedIndex, onSelectionChange }) {
  return (
    <View style={styles.segmentedControl}>
      {options.map((option, index) => (
        <TouchableOpacity
          key={option}
          style={[
            styles.segmentedOption,
            selectedIndex === index && styles.segmentedOptionSelected,
          ]}
          onPress={() => onSelectionChange(index)}
        >
          <Text
            style={[
              styles.segmentedText,
              selectedIndex === index && styles.segmentedTextSelected,
            ]}
          >
            {option}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
}

export default function GamecenterScreen() {
  const router = useRouter();
  const [selectedTeamIndex, setSelectedTeamIndex] = useState(0);
  const teamOptions = ["Oilers", "Panthers"];
  const selectedTeam = teamOptions[selectedTeamIndex];
  const currentRoster = rosterData[selectedTeam];

  return (
    <SafeAreaView style={styles.safeArea}>
      {/* Navigation Header */}
      <View style={styles.navigationBar}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color="#007AFF" />
          <Text style={styles.backButtonText}>All games</Text>
        </TouchableOpacity>
      </View>

      {/* Gamecenter Title */}
      <Text style={styles.gamecenterTitle}>Gamecenter</Text>

      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Game Card */}
        <View style={styles.card}>
          <Text style={styles.gameDate}>{game.date}</Text>

          <View style={styles.scoreRow}>
            <View style={styles.teamColumn}>
              <Image
                source={{ uri: game.teams[0].logo }}
                style={styles.teamLogo}
              />
              <Text style={styles.teamName}>{game.teams[0].name}</Text>
            </View>

            <Text style={styles.score}>
              {game.teams[0].score} - {game.teams[1].score}
            </Text>

            <View style={styles.teamColumn}>
              <Image
                source={{ uri: game.teams[1].logo }}
                style={styles.teamLogo}
              />
              <Text style={styles.teamName}>{game.teams[1].name}</Text>
            </View>
          </View>

          {/* Divider */}
          <View style={styles.divider} />

          {/* Period Table */}
          <View style={styles.periodTable}>
            <View style={styles.periodHeader}>
              <Text style={styles.periodHeaderCell}>Team</Text>
              <Text style={styles.periodHeaderCell}>1</Text>
              <Text style={styles.periodHeaderCell}>2</Text>
              <Text style={styles.periodHeaderCell}>3</Text>
              <Text style={styles.periodHeaderCell}>OT</Text>
              <Text style={styles.periodHeaderCell}>T</Text>
            </View>
            {game.teams.map((team, idx) => (
              <View key={team.name} style={styles.periodRow}>
                <Text style={styles.periodCell}>{team.fullName}</Text>
                {team.periodScores.map((score, i) => (
                  <Text key={i} style={styles.periodCell}>
                    {score}
                  </Text>
                ))}
                <Text style={styles.periodCell}>{team.score}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Roster Section */}
        <View style={styles.rosterSection}>
          <Text style={styles.rosterTitle}>Roster</Text>

          <SegmentedControl
            options={teamOptions}
            selectedIndex={selectedTeamIndex}
            onSelectionChange={setSelectedTeamIndex}
          />

          <View style={styles.rosterTable}>
            <View style={styles.rosterHeader}>
              <Text style={styles.rosterHeaderCell}>#</Text>
              <Text style={styles.rosterHeaderCell}>FORWARDS</Text>
              <Text style={styles.rosterHeaderCell}>POS</Text>
              <Text style={styles.rosterHeaderCell}>GP</Text>
              <Text style={styles.rosterHeaderCell}>G</Text>
              <Text style={styles.rosterHeaderCell}>A</Text>
            </View>
            {currentRoster.map((player) => (
              <View key={player.number} style={styles.rosterRow}>
                <Text style={styles.rosterCell}>{player.number}</Text>
                <Text style={styles.rosterCell}>{player.name}</Text>
                <Text style={styles.rosterCell}>{player.position}</Text>
                <Text style={styles.rosterCell}>
                  {player.gp !== null ? player.gp : "-"}
                </Text>
                <Text style={styles.rosterCell}>
                  {player.g !== null ? player.g : "-"}
                </Text>
                <Text style={styles.rosterCell}>
                  {player.a !== null ? player.a : "-"}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#F2F2F7",
  },
  navigationBar: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
  },
  backButtonText: {
    color: "#007AFF",
    fontSize: 17,
    marginLeft: 4,
  },
  gamecenterTitle: {
    fontSize: 28,
    fontWeight: "bold",
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 16,
    color: "#000",
  },
  container: {
    flex: 1,
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    marginHorizontal: 16,
    padding: 16,
    shadowColor: "#000000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  gameDate: {
    fontSize: 14,
    color: "#8E8E93",
    textAlign: "center",
    marginBottom: 8,
  },
  scoreRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  teamColumn: {
    alignItems: "center",
  },
  teamLogo: {
    width: 40,
    height: 40,
    resizeMode: "contain",
  },
  teamName: {
    fontSize: 16,
    marginTop: 4,
    color: "#000",
  },
  score: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000",
  },
  divider: {
    height: 1,
    backgroundColor: "#E0E0E0",
    marginVertical: 8,
  },
  periodTable: {
    width: "100%",
  },
  periodHeader: {
    flexDirection: "row",
    marginBottom: 4,
  },
  periodHeaderCell: {
    flex: 1,
    textAlign: "center",
    fontWeight: "500",
    color: "#8E8E93",
    fontSize: 14,
    paddingVertical: 4,
  },
  periodRow: {
    flexDirection: "row",
    marginBottom: 4,
  },
  periodCell: {
    flex: 1,
    textAlign: "center",
    fontSize: 14,
    paddingVertical: 4,
    color: "#000",
  },
  rosterSection: {
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  rosterTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#000",
    marginBottom: 12,
  },
  segmentedControl: {
    flexDirection: "row",
    backgroundColor: "#F2F2F7",
    borderRadius: 8,
    marginBottom: 12,
    padding: 2,
  },
  segmentedOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: "center",
  },
  segmentedOptionSelected: {
    backgroundColor: "#FFFFFF",
    shadowColor: "#000000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  segmentedText: {
    fontSize: 14,
    color: "#8E8E93",
  },
  segmentedTextSelected: {
    color: "#000",
    fontWeight: "500",
  },
  rosterTable: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 8,
  },
  rosterHeader: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
    marginBottom: 8,
  },
  rosterHeaderCell: {
    flex: 1,
    textAlign: "center",
    fontWeight: "600",
    color: "#8E8E93",
    fontSize: 12,
    paddingVertical: 8,
  },
  rosterRow: {
    flexDirection: "row",
    marginBottom: 4,
  },
  rosterCell: {
    flex: 1,
    textAlign: "center",
    fontSize: 14,
    paddingVertical: 4,
    color: "#000",
  },
});
