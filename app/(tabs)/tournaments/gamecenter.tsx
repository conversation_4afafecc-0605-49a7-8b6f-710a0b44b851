import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
} from "react-native";

// Dummy data for demonstration
const game = {
  gameNumber: 1,
  date: "Tue, 3 Jun 2025",
  teams: [
    {
      name: "<PERSON>",
      logo: "florida_panthers_logo.png",
      score: 4,
      periodScores: [1, 1, 1, 1],
    },
    {
      name: "Oilers",
      logo: "oilers_logo.png",
      score: 3,
      periodScores: [2, 1, 0, 0],
    },
  ],
};

export default function GamecenterScreen() {
  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.headerRow}></View>
      {/* Card */}
      <View style={styles.card}>
        <View style={styles.centerRow}></View>
        <View style={styles.centerRow}>
          <Text style={styles.gameDate}>Tue, 3 Jun</Text>
        </View>
        <View style={styles.scoreRow}>
          <View style={styles.teamInfo}>
            <Image
              source={{ uri: game.teams[0].logo }}
              style={styles.teamLogo}
            />
            <Text style={styles.teamName}>{game.teams[0].name}</Text>
          </View>
          <Text style={styles.score}>{game.teams[0].score}</Text>
          <Text style={styles.scoreDash}>-</Text>
          <Text style={styles.score}>{game.teams[1].score}</Text>
          <View style={styles.teamInfo}>
            <Image
              source={{ uri: game.teams[1].logo }}
              style={styles.teamLogo}
            />
            <Text style={styles.teamName}>{game.teams[1].name}</Text>
          </View>
        </View>
        {/* Divider above period table */}
        <View style={styles.periodDivider} />
        {/* Period Table */}
        <View style={styles.periodTable}>
          <View style={styles.periodHeader}>
            <Text style={styles.periodHeaderCell}>Team</Text>
            <Text style={styles.periodHeaderCell}>1</Text>
            <Text style={styles.periodHeaderCell}>2</Text>
            <Text style={styles.periodHeaderCell}>3</Text>
            <Text style={styles.periodHeaderCell}>OT</Text>
            <Text style={styles.periodHeaderCell}>T</Text>
          </View>
          {game.teams.map((team, idx) => (
            <View key={team.name} style={styles.periodRow}>
              <Text style={styles.periodCell}>{team.name}</Text>
              {team.periodScores.map((score, i) => (
                <Text key={i} style={styles.periodCell}>
                  {score}
                </Text>
              ))}
              <Text style={[styles.periodCell]}>{team.score}</Text>
            </View>
          ))}
        </View>
        {/* Divider below period table */}
        <View style={styles.periodDivider} />
      </View>
      {/* Roster Card */}
      <View style={styles.card}>
        <Text style={styles.rosterTitle}>Roster</Text>
        <View style={styles.tabsRow}>
          <Text style={styles.activeTab}>Oilers</Text>
          <Text style={styles.inactiveTab}>Panthers</Text>
        </View>
        <View style={styles.rosterTable}>
          <View style={styles.rosterHeader}>
            <Text style={styles.rosterHeaderCell}>#</Text>
            <Text style={styles.rosterHeaderCell}>FORWARDS</Text>
            <Text style={styles.rosterHeaderCell}>POS</Text>
            <Text style={styles.rosterHeaderCell}>GP</Text>
            <Text style={styles.rosterHeaderCell}>G</Text>
            <Text style={styles.rosterHeaderCell}>A</Text>
          </View>
          {/* Example rows */}
          <View style={styles.rosterRow}>
            <Text style={styles.rosterCell}>10</Text>
            <Text style={styles.rosterCell}>D. Ryan</Text>
            <Text style={styles.rosterCell}>C</Text>
            <Text style={styles.rosterCell}>-</Text>
            <Text style={styles.rosterCell}>-</Text>
            <Text style={styles.rosterCell}>-</Text>
          </View>
          <View style={styles.rosterRow}>
            <Text style={styles.rosterCell}>13</Text>
            <Text style={styles.rosterCell}>M. Janmark</Text>
            <Text style={styles.rosterCell}>C</Text>
            <Text style={styles.rosterCell}>18</Text>
            <Text style={styles.rosterCell}>3</Text>
            <Text style={styles.rosterCell}>1</Text>
          </View>
          {/* Add more rows as needed */}
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
    paddingTop: 15,
  },
  headerRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingHorizontal: 8,
    paddingTop: 0,
    paddingBottom: 0,
  },
  headerBack: {
    color: "#d32f2f",
    fontSize: 22,
    fontWeight: "bold",
    marginRight: 4,
  },
  headerTitle: {
    color: "#d32f2f",
    fontSize: 16,
    fontWeight: "500",
  },
  card: {
    backgroundColor: "#fff",
    borderRadius: 12,
    marginBottom: 18,
    marginHorizontal: 16,
    paddingVertical: 18,
    paddingHorizontal: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 4,
    elevation: 2,
  },
  centerRow: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 4,
  },
  gamecenterTitle: {
    fontWeight: "bold",
    fontSize: 22,
    color: "#222",
  },
  gameDate: {
    fontSize: 15,
    color: "#888",
  },
  scoreRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    gap: 16,
    marginBottom: 8,
  },
  teamInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  teamLogo: {
    width: 36,
    height: 36,
    resizeMode: "contain",
  },
  teamName: {
    fontWeight: "bold",
    fontSize: 20,
    color: "#222",
  },
  score: {
    fontWeight: "bold",
    fontSize: 28,
    color: "#231716",
  },
  scoreDash: {
    fontWeight: "500",
    fontSize: 30,
    color: "#231716",
    marginHorizontal: 2,
  },
  periodTable: {
    width: "100%",
    borderRadius: 8,
  },
  periodHeader: {
    flexDirection: "row",
    marginBottom: 0,
  },
  periodHeaderCell: {
    flex: 1,
    textAlign: "center",
    fontWeight: "500",
    color: "#7F7271",
    fontSize: 16,
    paddingVertical: 6,
  },
  periodRow: {
    flexDirection: "row",
    marginBottom: 0,
  },
  periodCell: {
    flex: 1,
    textAlign: "center",
    fontSize: 16,
    paddingVertical: 4,
  },
  periodDivider: {
    height: 1,
    backgroundColor: "#e0e0e0",
    marginVertical: 10,
    width: "100%",
  },
  rosterTitle: {
    fontWeight: "bold",
    fontSize: 19,
    color: "#222",
    marginBottom: 8,
  },
  tabsRow: {
    flexDirection: "row",
    gap: 10,
    marginBottom: 8,
  },
  activeTab: {
    fontWeight: "bold",
    color: "#222",
    borderBottomWidth: 2,
    borderBottomColor: "#d32f2f",
    paddingBottom: 4,
  },
  inactiveTab: {
    color: "#888",
    paddingBottom: 4,
  },
  rosterTable: {
    width: "100%",
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 8,
  },
  rosterHeader: {
    flexDirection: "row",
    borderBottomWidth: 2,
    borderBottomColor: "#e0e0e0",
    fontWeight: "bold",
    marginBottom: 2,
  },
  rosterHeaderCell: {
    flex: 1,
    textAlign: "center",
    fontWeight: "bold",
    color: "#888",
    paddingVertical: 6,
  },
  rosterRow: {
    flexDirection: "row",
    marginBottom: 2,
  },
  rosterCell: {
    flex: 1,
    textAlign: "center",
    fontSize: 15,
    paddingVertical: 4,
  },
});
