import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  ScrollView,
} from "react-native";
import { useRouter } from "expo-router";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { Tournament } from "../../../../data/tournaments/tournaments";
import { useFonts, BakbakOne_400Regular } from "@expo-google-fonts/bakbak-one";
import { TournamentDetails } from "../../../../data/tournaments/tournaments";
const { width } = Dimensions.get("window");

interface TournamentCardProps {
  tournament: TournamentDetails;
  onEdit?: (tournament: TournamentDetails) => void;
}

export default function TournamentCard({
  tournament,
  onEdit,
}: TournamentCardProps) {
  const router = useRouter();

  // Safety checks to ensure all values are strings
  const safeTitle =
    typeof tournament.title === "string" ? tournament.title : "Tournament";
  const safeSubtitle =
    typeof tournament.subtitle === "string" ? tournament.subtitle : "Details";
  const safeDate =
    typeof tournament.date === "string" ? tournament.date : "Date TBD";
  const safeTags = Array.isArray(tournament.tags) ? tournament.tags : [];

  const handleEdit = (e: any) => {
    e.stopPropagation();
    onEdit?.(tournament);
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => router.push(`/tournaments/${tournament.id}`)}
      activeOpacity={0.7}
    >
      <View style={styles.mainContent}>
        <Image source={{ uri: tournament.image }} style={styles.image} />
        <View style={styles.textContent}>
          <Text style={styles.title} numberOfLines={2}>
            {safeTitle}
          </Text>
          <Text style={styles.subtitle} numberOfLines={2}>
            {safeSubtitle}
          </Text>
          <Text style={styles.date}>{safeDate}</Text>
        </View>
        {onEdit && (
          <TouchableOpacity
            style={styles.editButton}
            onPress={handleEdit}
            activeOpacity={0.7}
          >
            <MaterialIcons name="mode-edit" size={19} color="#fff" />
          </TouchableOpacity>
        )}
      </View>
      <ScrollView
        horizontal
        style={styles.tagsContainer}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.tagsScrollContent}
      >
        {safeTags.map((tag, index) => (
          <View key={index} style={styles.tag}>
            <Text style={styles.tagText}>{String(tag)}</Text>
          </View>
        ))}
      </ScrollView>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    flexDirection: "column",
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    overflow: "hidden",
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  mainContent: {
    flexDirection: "row",
    padding: 16,
  },
  image: {
    width: 120,
    height: 90,
    borderRadius: 6,
    backgroundColor: "#8B8B8B",
    marginRight: 16,
  },
  textContent: {
    flex: 1,
    justifyContent: "flex-start",
  },
  title: {
    fontSize: 21,
    color: "#D52B1E",
    marginTop: 2,
    lineHeight: 22,
    fontFamily: "BakbakOne_400Regular",
  },
  subtitle: {
    fontSize: 15,
    color: "#231716",
    fontWeight: "700",
    marginBottom: 10,
  },
  date: {
    fontSize: 15,
    color: "#231716",
    fontWeight: "500",
  },
  tagsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  tagsScrollContent: {
    flexDirection: "row",
    gap: 6,
  },
  tag: {
    backgroundColor: "#231716",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  tagText: {
    fontSize: 12,
    color: "#FFFFFF",
    fontWeight: "500",
  },
  editButton: {
    position: "absolute",
    top: 16,
    right: 16,
    padding: 4,
    borderRadius: 4,
    backgroundColor: "#346eca",
    justifyContent: "center",
    alignItems: "center",
  },
});
