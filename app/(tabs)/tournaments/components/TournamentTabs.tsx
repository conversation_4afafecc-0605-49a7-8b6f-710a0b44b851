import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from "react-native";

const { width } = Dimensions.get("window");

interface TournamentTabsProps {
  tabs: string[];
  selectedTab: string;
  onTabSelect: (tab: string) => void;
}

export default function TournamentTabs({
  tabs,
  selectedTab,
  onTabSelect,
}: TournamentTabsProps) {
  const tabWidth = width / tabs.length;

  return (
    <View style={styles.container}>
      <View style={styles.tabsWrapper}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[styles.tab, { width: tabWidth }]}
            onPress={() => onTabSelect(tab)}
            activeOpacity={0.8}
          >
            <Text
              style={[
                styles.tabText,
                selectedTab === tab && styles.activeTabText,
              ]}
            >
              {tab}
            </Text>
            {selectedTab === tab && <View style={styles.underline} />}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    position: "relative",
  },
  tabsWrapper: {
    flexDirection: "row",
    alignItems: "center",
  },
  tab: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
    position: "relative",
  },
  tabText: {
    fontSize: 16,
    fontWeight: "400",
    color: "#231716",
    textAlign: "center",
  },
  activeTabText: {
    fontWeight: "bold",
    color: "#231716",
  },
  underline: {
    position: "absolute",
    bottom: 6,
    width: "70%",
    height: 2,
    backgroundColor: "#231716",
    borderRadius: 1,
    alignSelf: "center",
  },
});
