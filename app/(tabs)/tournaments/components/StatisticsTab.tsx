import React from "react";
import { View, Text, StyleSheet, ScrollView } from "react-native";

export default function PlayersTab() {
  const players = [
    {
      name: "<PERSON>",
      team: "Hong Kong Dragons",
      position: "Forward",
      number: 9,
    },
    {
      name: "<PERSON>",
      team: "Hong Kong Dragons",
      position: "Defense",
      number: 5,
    },
    {
      name: "<PERSON>",
      team: "Hong Kong Dragons",
      position: "<PERSON><PERSON>",
      number: 1,
    },
    {
      name: "<PERSON>",
      team: "Seoul Warriors",
      position: "Forward",
      number: 17,
    },
    {
      name: "<PERSON>",
      team: "Seoul Warriors",
      position: "Defense",
      number: 3,
    },
    {
      name: "<PERSON>",
      team: "Seoul Warriors",
      position: "<PERSON><PERSON>",
      number: 30,
    },
    { name: "<PERSON><PERSON>", team: "Tokyo Ice", position: "Forward", number: 11 },
    { name: "<PERSON><PERSON><PERSON>", team: "Tokyo Ice", position: "Defense", number: 4 },
    {
      name: "<PERSON>",
      team: "Tokyo Ice",
      position: "<PERSON><PERSON>",
      number: 29,
    },
  ];

  return (
    <ScrollView style={styles.tabContainer}>
      {players.map((player, index) => (
        <View key={index} style={styles.playerCard}>
          <View style={styles.playerInfo}>
            <Text style={styles.playerName}>{player.name}</Text>
            <Text style={styles.playerTeam}>{player.team}</Text>
            <Text style={styles.playerPosition}>{player.position}</Text>
          </View>
          <Text style={styles.playerNumber}>#{player.number}</Text>
        </View>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  tabContainer: {
    flex: 1,
    backgroundColor: "#F0F1F5",
    paddingTop: 15,
  },
  playerCard: {
    backgroundColor: "#fff",
    marginHorizontal: 10,
    marginBottom: 12,
    padding: 16,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
    borderWidth: 0.2,
    borderColor: "#CACDD4",
  },
  playerInfo: {
    flex: 1,
  },
  playerName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  playerTeam: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
  playerPosition: {
    fontSize: 14,
    color: "#666",
    marginTop: 2,
  },
  playerNumber: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#D52B1E",
    marginLeft: 12,
  },
});
