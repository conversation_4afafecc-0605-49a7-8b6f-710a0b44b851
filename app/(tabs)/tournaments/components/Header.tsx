import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useFonts, BakbakOne_400Regular } from "@expo-google-fonts/bakbak-one";

interface HeaderProps {
  title: string;
  onFilterPress?: () => void;
  onAddPress?: () => void;
  showFilter?: boolean;
  showAdd?: boolean;
}

export default function Header({
  title,
  onFilterPress,
  onAddPress,
  showFilter = false,
  showAdd = false,
}: HeaderProps) {
  const [fontsLoaded] = useFonts({
    BakbakOne_400Regular,
  });

  if (!fontsLoaded) {
    return null;
  }

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="#D52B1E" />
      <View style={styles.container}>
        <Text style={styles.title}>{title}</Text>
        <View style={styles.buttonContainer}>
          {showAdd && (
            <TouchableOpacity
              style={styles.addButton}
              onPress={onAddPress}
              activeOpacity={0.7}
            >
              <View style={styles.roundAddButton}>
                <Ionicons name="add" size={26} color="#fff" />
              </View>
            </TouchableOpacity>
          )}
          {showFilter && (
            <TouchableOpacity
              style={styles.filterButton}
              onPress={onFilterPress}
              activeOpacity={0.7}
            >
              <Ionicons name="filter" size={26} color="#FFFFFF" />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#D52B1E",
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  title: {
    fontSize: 30,
    fontWeight: "bold",
    color: "#FFFFFF",
    fontFamily: "BakbakOne_400Regular",
    letterSpacing: 0.5,
  },
  buttonContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  addButton: {
    marginTop: 3,
  },
  roundAddButton: {
    width: 30,
    height: 30,
    borderRadius: 20,
    backgroundColor: "#346eca",
    justifyContent: "center",
    alignItems: "center",
  },
  filterButton: {
    marginTop: 3,
  },
});
