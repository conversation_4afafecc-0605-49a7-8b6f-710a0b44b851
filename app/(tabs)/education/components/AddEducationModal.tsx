import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { addEducationItem, updateEducationItem, EducationDetails } from '../../../../data/education/education';

interface AddEducationModalProps {
  visible: boolean;
  onClose: () => void;
  editingEducation?: EducationDetails | null;
}

interface EducationFormData {
  title: string;
  subtitle: string;
  date: string;
  tags: string[];
  image: string;
  type: 'camp' | 'showcase';
  description: string;
  location: string;
  country: string;
  participants: number;
  status: string;
}

export default function AddEducationModal({ visible, onClose, editingEducation }: AddEducationModalProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [formData, setFormData] = useState<EducationFormData>({
    title: '',
    subtitle: '',
    date: '',
    tags: [],
    image: 'https://picsum.photos/200/150?random=1',
    type: 'camp',
    description: '',
    location: '',
    country: '',
    participants: 0,
    status: 'Upcoming',
  });

  const [tagInput, setTagInput] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState({
    day: new Date().getDate(),
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
  });

  // Track if date picker has been opened for this form session
  const [datePickerInitialized, setDatePickerInitialized] = useState(false);

  // Add refs for scroll views to control scroll position
  const dayScrollRef = useRef<ScrollView>(null);
  const monthScrollRef = useRef<ScrollView>(null);
  const yearScrollRef = useRef<ScrollView>(null);

  const days = Array.from({ length: 31 }, (_, i) => i + 1);
  const months = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' },
  ];
  const years = Array.from({ length: 10 }, (_, i) => new Date().getFullYear() + i);

  useEffect(() => {
    if (visible) {
      // Reset date picker initialization when modal opens
      setDatePickerInitialized(false);
      
      if (editingEducation) {
        setFormData({
          title: editingEducation.title || '',
          subtitle: editingEducation.subtitle || '',
          date: editingEducation.date || '',
          tags: editingEducation.tags || [],
          image: editingEducation.image || 'https://picsum.photos/200/150?random=1',
          type: editingEducation.type || 'camp',
          description: editingEducation.description || '',
          location: editingEducation.location || '',
          country: editingEducation.country || '',
          participants: editingEducation.participants || 0,
          status: editingEducation.status || 'Upcoming',
        });
      } else {
        setFormData({
          title: '',
          subtitle: '',
          date: '',
          tags: [],
          image: 'https://picsum.photos/200/150?random=1',
          type: 'camp',
          description: '',
          location: '',
          country: '',
          participants: 0,
          status: 'Upcoming',
        });
      }
      setTagInput('');
    }
  }, [visible, editingEducation]);

  const handleDatePickerOpen = () => {
    // Only reset to today's date if this is the first time opening the date picker
    if (!datePickerInitialized) {
      const today = new Date();
      setSelectedDate({
        day: today.getDate(),
        month: today.getMonth() + 1,
        year: today.getFullYear(),
      });
      setDatePickerInitialized(true);
    }
    setShowDatePicker(true);

    // Scroll to selected items after a short delay to ensure the modal is rendered
    setTimeout(() => {
      scrollToSelectedItems();
    }, 100);
  };

  const scrollToSelectedItems = () => {
    // Calculate scroll positions for each picker
    const dayIndex = selectedDate.day - 1;
    const monthIndex = selectedDate.month - 1;
    const yearIndex = years.findIndex(year => year === selectedDate.year);

    const itemHeight = 50; // Approximate item height including margins

    // Scroll to selected items
    dayScrollRef.current?.scrollTo({ y: dayIndex * itemHeight, animated: true });
    monthScrollRef.current?.scrollTo({ y: monthIndex * itemHeight, animated: true });
    yearScrollRef.current?.scrollTo({ y: yearIndex * itemHeight, animated: true });
  };

  const handleDateConfirm = () => {
    const newDate = new Date(selectedDate.year, selectedDate.month - 1, selectedDate.day);
    
    if (newDate.getDate() !== selectedDate.day || newDate.getMonth() !== selectedDate.month - 1) {
      Alert.alert('Error', 'Invalid date selected');
      return;
    }

    const formattedDate = newDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    setFormData(prev => ({ ...prev, date: formattedDate }));
    setShowDatePicker(false);
  };

  const handleDateCancel = () => {
    setShowDatePicker(false);
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = async () => {
    if (!formData.title || !formData.subtitle || !formData.date) {
      Alert.alert('Error', 'Please fill in all required fields (Title, Subtitle, Date)');
      return;
    }

    setIsCreating(true);
    try {
      if (editingEducation) {
        await updateEducationItem(editingEducation.id, formData);
        Alert.alert('Success', 'Education item updated successfully!');
      } else {
        await addEducationItem(formData);
        Alert.alert('Success', 'Education item created successfully!');
      }
      onClose();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to save education item');
    } finally {
      setIsCreating(false);
    }
  };

  const DatePickerModal = () => (
    <Modal
      visible={showDatePicker}
      transparent
      animationType="slide"
      onRequestClose={() => setShowDatePicker(false)}
    >
      <View style={styles.datePickerOverlay}>
        <View style={styles.datePickerContainer}>
          <Text style={styles.datePickerTitle}>Select Date</Text>
          
          <View style={styles.datePickerContent}>
            <View style={styles.pickerColumn}>
              <Text style={styles.pickerLabel}>Day</Text>
              <ScrollView 
                ref={dayScrollRef}
                style={styles.pickerScroll} 
                showsVerticalScrollIndicator={false}
              >
                {days.map((day) => (
                  <TouchableOpacity
                    key={day}
                    style={[styles.pickerItem, selectedDate.day === day && styles.pickerItemSelected]}
                    onPress={() => setSelectedDate(prev => ({ ...prev, day }))
                    }
                  >
                    <Text style={[styles.pickerItemText, selectedDate.day === day && styles.pickerItemTextSelected]}>
                      {day}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            <View style={styles.pickerColumn}>
              <Text style={styles.pickerLabel}>Month</Text>
              <ScrollView 
                ref={monthScrollRef}
                style={styles.pickerScroll} 
                showsVerticalScrollIndicator={false}
              >
                {months.map((month) => (
                  <TouchableOpacity
                    key={month.value}
                    style={[styles.pickerItem, selectedDate.month === month.value && styles.pickerItemSelected]}
                    onPress={() => setSelectedDate(prev => ({ ...prev, month: month.value }))}
                  >
                    <Text style={[styles.pickerItemText, selectedDate.month === month.value && styles.pickerItemTextSelected]}>
                      {month.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            <View style={styles.pickerColumn}>
              <Text style={styles.pickerLabel}>Year</Text>
              <ScrollView 
                ref={yearScrollRef}
                style={styles.pickerScroll} 
                showsVerticalScrollIndicator={false}
              >
                {years.map((year) => (
                  <TouchableOpacity
                    key={year}
                    style={[styles.pickerItem, selectedDate.year === year && styles.pickerItemSelected]}
                    onPress={() => setSelectedDate(prev => ({ ...prev, year }))}
                  >
                    <Text style={[styles.pickerItemText, selectedDate.year === year && styles.pickerItemTextSelected]}>
                      {year}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
          
          <View style={styles.datePickerButtons}>
            <TouchableOpacity
              style={[styles.datePickerButton, styles.datePickerCancel]}
              onPress={() => setShowDatePicker(false)}
            >
              <Text style={styles.datePickerCancelText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.datePickerButton, styles.datePickerConfirm]}
              onPress={handleDateConfirm}
            >
              <Text style={styles.datePickerConfirmText}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const isEditing = !!editingEducation;

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.headerButton}>
            <Ionicons name="close" size={24} color="#000" />
          </TouchableOpacity>
          <Text style={styles.title}>
            {isEditing ? 'Edit Education Item' : 'Add Education Item'}
          </Text>
          <TouchableOpacity 
            onPress={handleSubmit} 
            disabled={isCreating}
            style={styles.headerButton}
          >
            {isCreating ? (
              <ActivityIndicator size="small" color="#D62828" />
            ) : (
              <Text style={styles.saveButton}>
                {isEditing ? 'Update' : 'Save'}
              </Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Title *</Text>
            <TextInput
              style={styles.input}
              value={formData.title}
              onChangeText={(text) => setFormData(prev => ({ ...prev, title: text }))}
              placeholder="Enter title"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Subtitle *</Text>
            <TextInput
              style={styles.input}
              value={formData.subtitle}
              onChangeText={(text) => setFormData(prev => ({ ...prev, subtitle: text }))}
              placeholder="Enter subtitle"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Type *</Text>
            <View style={styles.typeSelector}>
              <TouchableOpacity
                style={[styles.typeButton, formData.type === 'camp' && styles.typeButtonActive]}
                onPress={() => setFormData(prev => ({ ...prev, type: 'camp' }))}
              >
                <Text style={[styles.typeButtonText, formData.type === 'camp' && styles.typeButtonTextActive]}>
                  Camp
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.typeButton, formData.type === 'showcase' && styles.typeButtonActive]}
                onPress={() => setFormData(prev => ({ ...prev, type: 'showcase' }))}
              >
                <Text style={[styles.typeButtonText, formData.type === 'showcase' && styles.typeButtonTextActive]}>
                  Showcase
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Date *</Text>
            <TouchableOpacity 
              style={styles.dateInput}
              onPress={handleDatePickerOpen}
            >
              <Text style={styles.dateText}>
                {formData.date || 'Select date'}
              </Text>
              <Ionicons name="calendar" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              placeholder="Enter description"
              multiline
              numberOfLines={4}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Location</Text>
            <TextInput
              style={styles.input}
              value={formData.location}
              onChangeText={(text) => setFormData(prev => ({ ...prev, location: text }))}
              placeholder="Enter location"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Country</Text>
            <TextInput
              style={styles.input}
              value={formData.country}
              onChangeText={(text) => setFormData(prev => ({ ...prev, country: text }))}
              placeholder="Enter country"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Expected Participants</Text>
            <TextInput
              style={styles.input}
              value={formData.participants.toString()}
              onChangeText={(text) => setFormData(prev => ({ ...prev, participants: parseInt(text) || 0 }))}
              placeholder="0"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Status</Text>
            <TextInput
              style={styles.input}
              value={formData.status}
              onChangeText={(text) => setFormData(prev => ({ ...prev, status: text }))}
              placeholder="Status"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Image URL</Text>
            <TextInput
              style={styles.input}
              value={formData.image}
              onChangeText={(text) => setFormData(prev => ({ ...prev, image: text }))}
              placeholder="Enter image URL"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Tags</Text>
            <View style={styles.tagInputContainer}>
              <TextInput
                style={[styles.input, styles.tagInput]}
                value={tagInput}
                onChangeText={setTagInput}
                placeholder="Add a tag"
                onSubmitEditing={handleAddTag}
              />
              <TouchableOpacity
                style={styles.addTagButton}
                onPress={handleAddTag}
              >
                <Ionicons name="add" size={20} color="#fff" />
              </TouchableOpacity>
            </View>
            
            {formData.tags.length > 0 && (
              <View style={styles.tagsContainer}>
                {formData.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                    <TouchableOpacity
                      style={styles.removeTagButton}
                      onPress={() => handleRemoveTag(tag)}
                    >
                      <Ionicons name="close" size={16} color="#fff" />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
          </View>

          <View style={styles.bottomPadding} />
        </ScrollView>
      </View>

      <DatePickerModal />
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F7F7',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E7',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerButton: {
    padding: 8,
    minWidth: 40,
    minHeight: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButton: {
    color: '#D62828',
    fontSize: 16,
    fontWeight: '600',
  },
  form: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#E5E5E7',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  typeSelector: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 4,
  },
  typeButton: {
    flex: 1,
    padding: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  typeButtonActive: {
    backgroundColor: '#D62828',
  },
  typeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  typeButtonTextActive: {
    color: '#fff',
  },
  dateInput: {
    borderWidth: 1,
    borderColor: '#E5E5E7',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },
  datePickerOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  datePickerContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
    padding: 20,
  },
  datePickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  datePickerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: 200,
    marginBottom: 20,
  },
  pickerColumn: {
    flex: 1,
    marginHorizontal: 5,
  },
  pickerLabel: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  pickerScroll: {
    flex: 1,
    backgroundColor: '#F9F9F9',
    borderRadius: 8,
  },
  pickerItem: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: 'center',
    borderRadius: 6,
    margin: 2,
  },
  pickerItemSelected: {
    backgroundColor: '#D62828',
  },
  pickerItemText: {
    fontSize: 16,
    color: '#333',
  },
  pickerItemTextSelected: {
    color: '#fff',
    fontWeight: '600',
  },
  datePickerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  datePickerButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  datePickerCancel: {
    backgroundColor: '#F5F5F5',
  },
  datePickerConfirm: {
    backgroundColor: '#D62828',
  },
  datePickerCancelText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
  datePickerConfirmText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  tagInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  tagInput: {
    flex: 1,
    marginBottom: 0,
  },
  addTagButton: {
    backgroundColor: '#D62828',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 44,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  tag: {
    backgroundColor: '#231716',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  tagText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  removeTagButton: {
    padding: 2,
  },
  bottomPadding: {
    height: 50,
  },
});
