import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, FlatList } from "react-native";
import Header from "./components/Header";
import EducationTabs from "./components/EducationTabs";
import EducationCard from "./components/EducationCard";
import AddEducationModal from "./components/AddEducationModal";
import {
  getEducationItems,
  educationData,
  EducationItem,
  EducationDetails,
} from "./../../../data/education/education";

function HockeyCampsContent({
  educationData,
  onEdit,
}: {
  educationData: EducationItem[];
  onEdit: (item: EducationItem) => void;
}) {
  const campData = educationData.filter((item) => item.type === "camp");

  return (
    <ScrollView style={styles.contentContainer}>
      <View style={styles.content}>
        {campData.map((item) => (
          <EducationCard key={item.id} item={item} onEdit={onEdit} />
        ))}
      </View>
    </ScrollView>
  );
}

function ShowcasesContent({
  educationData,
  onEdit,
}: {
  educationData: EducationItem[];
  onEdit: (item: EducationItem) => void;
}) {
  const showcaseData = educationData.filter((item) => item.type === "showcase");

  return (
    <ScrollView style={styles.contentContainer}>
      <View style={styles.content}>
        {showcaseData.map((item) => (
          <EducationCard key={item.id} item={item} onEdit={onEdit} />
        ))}
      </View>
    </ScrollView>
  );
}

export default function EducationScreen() {
  const [activeTab, setActiveTab] = useState("camps");
  const [showAddModal, setShowAddModal] = useState(false);
  const [educationData, setEducationData] = useState<EducationItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingEducation, setEditingEducation] =
    useState<EducationDetails | null>(null);

  useEffect(() => {
    loadEducationData();
  }, []);

  const loadEducationData = async () => {
    try {
      setLoading(true);
      const data = await getEducationItems();
      setEducationData(data);
    } catch (error) {
      console.error("Error loading education data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterPress = () => {
    // Filter functionality can be implemented here
    console.log("Filter pressed in Education tab");
  };

  const handleAddPress = () => {
    setEditingEducation(null);
    setShowAddModal(true);
  };

  const handleEditPress = (item: EducationItem) => {
    setEditingEducation(item as EducationDetails);
    setShowAddModal(true);
  };

  const handleAddModalClose = () => {
    setShowAddModal(false);
    setEditingEducation(null);
    // Reload data after modal closes
    loadEducationData();
  };

  const handleTabPress = (tab: string) => {
    setActiveTab(tab);
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      );
    }

    switch (activeTab) {
      case "camps":
        return (
          <HockeyCampsContent
            educationData={educationData}
            onEdit={handleEditPress}
          />
        );
      case "showcases":
        return (
          <ShowcasesContent
            educationData={educationData}
            onEdit={handleEditPress}
          />
        );
      default:
        return (
          <HockeyCampsContent
            educationData={educationData}
            onEdit={handleEditPress}
          />
        );
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="Education"
        onFilterPress={handleFilterPress}
        onAddPress={handleAddPress}
      />
      <EducationTabs activeTab={activeTab} onTabPress={handleTabPress} />
      {renderContent()}

      <AddEducationModal
        visible={showAddModal}
        onClose={handleAddModalClose}
        editingEducation={editingEducation}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  contentContainer: {
    flex: 1,
    backgroundColor: "#F0F1F5",
  },
  content: {
    paddingHorizontal: 10,
    paddingVertical: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});
