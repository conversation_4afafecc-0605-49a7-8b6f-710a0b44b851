# Agent Task: Gamecenter UI Refactor

## Focus
This agent task is focused on refactoring the Gamecenter screen UI to match the provided design reference (image and JSON description to be supplied). The goal is to ensure the style, layout, and user experience closely follow the new design, while preserving and reusing as much of the existing logic and structure as possible.

## Context
- The Gamecenter screen already displays game info, scores, period breakdown, and roster tables.
- The current UI is functional but does not match the new design requirements for style, layout, and visual polish.
- You will be provided with an image and a JSON description as the design benchmark. All UI elements, spacing, colors, typography, and component structure should be updated to match this reference.

This is the JSON description:

```json
{
  "device": {
    "platform": "iOS",
    "model": "iPhone",
    "orientation": "portrait"
  },
  "statusBar": {
    "time": "9:41",
    "signalStrength": "full",
    "wifi": true,
    "batteryLevel": "100%"
  },
  "navigationBar": {
    "left": {
      "type": "button",
      "icon": "chevron-left",
      "label": "All games",
      "action": "back"
    },
    "title": ""
  },
  "header": {
    "type": "text",
    "text": "Gamecenter",
    "style": {
      "fontSize": 28,
      "fontWeight": "bold",
      "marginHorizontal": 16,
      "marginTop": 8,
      "marginBottom": 16
    }
  },
  "body": {
    "scrollable": true,
    "sections": [
      {
        "type": "card",
        "style": {
          "marginHorizontal": 16,
          "padding": 16,
          "borderRadius": 8,
          "backgroundColor": "#FFFFFF",
          "shadow": {
            "color": "#000000",
            "opacity": 0.1,
            "radius": 4,
            "offset": { "width": 0, "height": 2 }
          }
        },
        "children": [
          {
            "type": "text",
            "text": "Tue, 3 Jun",
            "style": {
              "fontSize": 14,
              "color": "#8E8E93",
              "textAlign": "center",
              "marginBottom": 8
            }
          },
          {
            "type": "row",
            "style": {
              "justifyContent": "space-between",
              "alignItems": "center",
              "marginBottom": 12
            },
            "children": [
              {
                "type": "column",
                "style": { "alignItems": "center" },
                "children": [
                  {
                    "type": "image",
                    "source": "florida_panthers_logo.png",
                    "size": { "width": 40, "height": 40 }
                  },
                  {
                    "type": "text",
                    "text": "Panthers",
                    "style": { "fontSize": 16, "marginTop": 4 }
                  }
                ]
              },
              {
                "type": "text",
                "text": "4 - 3",
                "style": { "fontSize": 24, "fontWeight": "bold" }
              },
              {
                "type": "column",
                "style": { "alignItems": "center" },
                "children": [
                  {
                    "type": "image",
                    "source": "edmonton_oilers_logo.png",
                    "size": { "width": 40, "height": 40 }
                  },
                  {
                    "type": "text",
                    "text": "Oilers",
                    "style": { "fontSize": 16, "marginTop": 4 }
                  }
                ]
              }
            ]
          },
          {
            "type": "divider",
            "style": {
              "thickness": 1,
              "color": "#E0E0E0",
              "marginVertical": 8
            }
          },
          {
            "type": "table",
            "props": {
              "columns": ["Team", "1", "2", "3", "OT", "T"],
              "rows": [
                {
                  "Team": "Florida Panthers",
                  "1": 1,
                  "2": 1,
                  "3": 1,
                  "OT": 1,
                  "T": 4
                },
                {
                  "Team": "Edmonton Oilers",
                  "1": 2,
                  "2": 1,
                  "3": 0,
                  "OT": 0,
                  "T": 3
                }
              ]
            }
          }
        ]
      },
      {
        "type": "section",
        "style": { "paddingHorizontal": 16, "paddingTop": 8 },
        "props": {
          "title": "Roster"
        },
        "children": [
          {
            "type": "segmentedControl",
            "props": {
              "options": ["Oilers", "Panthers"],
              "selectedIndex": 0,
              "style": {
                "backgroundColor": "#F2F2F7",
                "selectedBackgroundColor": "#FFFFFF",
                "fontSize": 14,
                "marginBottom": 12
              }
            }
          },
          {
            "type": "table",
            "props": {
              "columns": ["#", "FORWARDS", "POS", "GP", "G", "A"],
              "rows": [
                { "#": 10, "FORWARDS": "D. Ryan", "POS": "C", "GP": null, "G": null, "A": null },
                { "#": 13, "FORWARDS": "M. Janmark", "POS": "C", "GP": 18, "G": 3, "A": 1 },
                { "#": 18, "FORWARDS": "Z. Hyman", "POS": "LW", "GP": 15, "G": 5, "A": 6 },
                { "#": 19, "FORWARDS": "A. Henrique", "POS": "C", "GP": 18, "G": 4, "A": 2 },
                { "#": 21, "FORWARDS": "T. Frederic", "POS": "C", "GP": 18, "G": 1, "A": 3 },
                { "#": 22, "FORWARDS": "M. Savoie", "POS": "C", "GP": null, "G": null, "A": null },
                { "#": 28, "FORWARDS": "C. Brown", "POS": "RW", "GP": 16, "G": 5, "A": 3 }
              ]
            }
          }
        ]
      }
    ]
  },
  "tabBar": {
    "style": { "backgroundColor": "#FFFFFF", "borderTopColor": "#E0E0E0", "borderTopWidth": 1 },
    "items": [
      { "icon": "tournaments_icon", "label": "Tournaments", "selected": true },
      { "icon": "education_icon", "label": "Education", "selected": false },
      { "icon": "leagues_icon", "label": "Leagues", "selected": false },
      { "icon": "favourites_icon", "label": "Favourites", "selected": false },
      { "icon": "more_icon", "label": "More", "selected": false }
    ]
  },
  "homeIndicator": {
    "visible": true
  }
}
```


## Tasks
1. **Review the Provided Design**
   - Carefully study the supplied image and JSON description.
   - Note all differences in layout, spacing, colors, fonts, and component arrangement compared to the current implementation.

2. **Refactor the UI**
   - Update the Gamecenter screen to match the new design as closely as possible.
   - Refactor styles, layout, and component structure as needed.
   - Ensure all sections (header, scorecard, period table, roster, tabs, etc.) follow the new design.
   - Use consistent spacing, colors, and typography as shown in the reference.
   - Make the UI visually appealing, modern, and easy to read.

3. **Preserve and Improve Functionality**
   - Keep all existing data display and logic (game info, scores, period breakdown, roster, etc.).
   - Ensure all interactive elements (tabs, buttons, etc.) work as expected.
   - If the design introduces new UI elements or interactions, implement them as described.

4. **Test Responsiveness and Edge Cases**
   - Make sure the UI looks good on different device sizes and orientations.
   - Handle long team names, missing images, and other edge cases gracefully.

5. **Follow Best Practices**
   - Use clear, maintainable code and styles.
   - Avoid inline styles where possible; use StyleSheet for consistency.
   - Ensure accessibility and good user experience.

## Completion Criteria
- The Gamecenter screen UI matches the provided design reference in style, layout, and visual details.
- All existing and new UI elements are functional and visually consistent.
- The code is clean, maintainable, and follows best practices.
- The screen is responsive and handles edge cases well.

*Follow this document strictly. Approach each task step by step. Do not skip or merge steps. Wait for the image and JSON description before starting the refactor.*
