## Problems:

* Tournaments loading:
  - 2 load animations

* Edit Tournament:
  - fix Select Tournament Date modal: everytime I click on a date variable, it reloads its animation.
  - Implement deletion for tournaments
  - Date ending and start for a tournament (Ending should be optional)

* Code layout for headers (look for code improvements):
  - Refactor code to use consistent headers with design in one place for reusability. 
  - Determine to use only one code solution for headers: either edit headers in screen file or in layout file
  - To be found more..

* Team Details in favourite team:
  - fix "< index" back routing that's probably created by a router automatically

## Improvements:

* 