{"name": "hockey-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "npx expo start -c", "start-lan": "npx expo start -c --lan", "start-tunnel": "npx expo start -c --tunnel", "pub": "npx expo publish", "android": "npx expo start -c --android", "ios": "npx expo start -c --ios", "web": "npx expo start -c --web", "test": "npx echo \"Error: no test specified\" && exit 1", "login": "npx expo login"}, "repository": {"type": "git", "url": "git+https://github.com/aza1re/HKPHL.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/aza1re/HKPHL/issues"}, "homepage": "https://github.com/aza1re/HKPHL#readme", "description": "", "dependencies": {"@expo-google-fonts/bakbak-one": "^0.4.0", "@expo/metro-runtime": "~5.0.4", "@expo/ngrok": "^4.1.3", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/material-top-tabs": "^7.2.15", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.15", "@tanstack/react-query": "^5.83.0", "expo": "^53.0.19", "expo-auth-session": "^6.2.1", "expo-font": "^13.3.2", "expo-image-picker": "~16.1.4", "expo-router": "^5.1.3", "expo-splash-screen": "^0.30.10", "expo-status-bar": "~2.2.3", "expo-web-browser": "^14.2.0", "firebase": "^11.10.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "^0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@playwright/test": "^1.53.0", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "babel-preset-expo": "^13.0.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "typescript": "^5.8.3"}}