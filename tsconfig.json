{"extends": "expo/tsconfig.base", "compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "bundler", "jsx": "react-native", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "resolveJsonModule": true, "noEmit": true, "isolatedModules": true}, "include": ["App.tsx", "global.d.ts", "firebase-shims.d.ts", "app/**/*", "context/**/*", "firebase/**/*", "types/**/*", "src/**/*"], "exclude": ["node_modules", "dist", "build", ".expo"]}