// Auth types for React Native mobile app

export interface User {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface SignupData {
  email: string;
  password: string;
  confirmPassword: string;
  displayName?: string;
}

// Additional mobile-specific auth types
export interface AuthError {
  code: string;
  message: string;
}

export interface GoogleAuthResponse {
  type: 'success' | 'cancel';
  authentication?: {
    accessToken: string;
    idToken?: string;
  };
}

// Simplified Kakao types for web SDK
export interface KakaoAuthResponse {
  access_token: string;
  token_type: string;
  refresh_token?: string;
  expires_in: number;
  scope?: string;
}

export interface KakaoUserProfile {
  id: number;
  connected_at: string;
  properties?: {
    nickname?: string;
    profile_image?: string;
    thumbnail_image?: string;
  };
  kakao_account?: {
    profile_nickname_needs_agreement?: boolean;
    profile_image_needs_agreement?: boolean;
    profile?: {
      nickname?: string;
      thumbnail_image_url?: string;
      profile_image_url?: string;
    };
    has_email?: boolean;
    email_needs_agreement?: boolean;
    is_email_valid?: boolean;
    is_email_verified?: boolean;
    email?: string;
  };
}

export interface ResetPasswordData {
  email: string;
}

export interface UpdateProfileData {
  displayName?: string;
  photoURL?: string;
}
