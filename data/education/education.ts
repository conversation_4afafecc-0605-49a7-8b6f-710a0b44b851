import {getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, orderBy } from 'firebase/firestore';

import { initializeApp, getApps, getApp } from 'firebase/app';


// Initialize Firebase (you'll need to add your config)
const firebaseConfig = {
  // apiKey: "AIzaSyAXYi7MCm-aMBeh3bEjs0eJ5eHcGjf9-bw",
  // authDomain: "achieve-cb085.firebaseapp.com",
  // projectId: "achieve-cb085",
  // storageBucket: "achieve-cb085.firebasestorage.app",
  // messagingSenderId: "260668035138",
  // appId: "1:260668035138:web:ba67a28ffd83d01b279ddb",
  // measurementId: "G-JWFXTNTCGZ"
};

const app = getApps().length > 0 ? getApp() : initializeApp(firebaseConfig);
const db = getFirestore(app);
export interface EducationItem {
  id: string;
  title: string;
  subtitle: string;
  date: string;
  image: string;
  tags: string[];
  type: "camp" | "showcase";
}

export interface EducationDetails extends EducationItem {
  description?: string;
  location?: string;
  country?: string;
  participants?: number;
  status?: string;
}

const COLLECTION_NAME = 'education';

// Get all education items
export const getEducationItems = async (): Promise<EducationItem[]> => {
  try {
    const q = query(collection(db, COLLECTION_NAME), orderBy('date', 'desc'));
    const querySnapshot = await getDocs(q);
    
    const items: EducationItem[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      items.push({
        id: doc.id,
        title: data.title,
        subtitle: data.subtitle,
        date: data.date,
        image: data.image,
        tags: data.tags || [],
        type: data.type,
      });
    });
    
    return items;
  } catch (error) {
    console.error('Error fetching education items:', error);
    throw new Error('Failed to fetch education items');
  }
};

// Add new education item
export const addEducationItem = async (itemData: Omit<EducationDetails, 'id'>): Promise<string> => {
  try {
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...itemData,
      createdAt: new Date().toISOString(),
    });
    
    return docRef.id;
  } catch (error) {
    console.error('Error adding education item:', error);
    throw new Error('Failed to add education item');
  }
};

// Update education item
export const updateEducationItem = async (id: string, itemData: Partial<EducationDetails>): Promise<void> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...itemData,
      updatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error updating education item:', error);
    throw new Error('Failed to update education item');
  }
};

// Delete education item
export const deleteEducationItem = async (id: string): Promise<void> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting education item:', error);
    throw new Error('Failed to delete education item');
  }
};

// Legacy export for backward compatibility
export const educationData: EducationItem[] = [];
