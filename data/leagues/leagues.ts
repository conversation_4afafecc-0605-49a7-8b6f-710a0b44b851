export interface League {
  id: string;
  name: string;
  fullName: string;
  description: string;
}

export const leagues: League[] = [
  {
    id: "hkphl",
    name: "HKPHL",
    fullName: "Hong Kong Premier Hockey League",
    description: "The premier ice hockey league in Hong Kong",
  },
  {
    id: "nhl",
    name: "NHL",
    fullName: "National Hockey League",
    description: "Biggest Hockey League in the US and World",
  },
];

export const getLeagueById = (id: string): League | undefined => {
  return leagues.find((league) => league.id === id);
};
