export interface Team {
  name: string;
  logo?: string;
  score: number | null;
}

export interface Game {
  gameNumber: number;
  date: string;
  teams: Team[];
}

export const games: Game[] = [
  {
    gameNumber: 1,
    date: "Tue, 3 Jun 2025",
    teams: [
      { name: "Panthers", logo: "panthers_logo.png", score: 4 },
      { name: "Oilers", logo: "oilers_logo.png", score: 3 },
    ],
  },
  {
    gameNumber: 2,
    date: "Wed, 7 Jun 2025",
    teams: [
      { name: "Columbus", logo: "columbus_logo.png", score: 3 },
      { name: "Oilers", logo: "oilers_logo.png", score: 7 },
    ],
  },
  {
    gameNumber: 3,
    date: "Tue, 10 Jun 2025",
    teams: [
      { name: "Oilers", logo: "oilers_logo.png", score: null },
      { name: "Panthers", logo: "panthers_logo.png", score: null },
    ],
  },
];