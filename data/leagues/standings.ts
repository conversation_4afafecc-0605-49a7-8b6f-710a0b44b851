export interface TeamStanding {
  rank: number;
  team: string;
  logo: string;
  gamesPlayed: number;
  wins: number;
  losses: number;
  ties: number;
  points: number;
  goalsFor: number;
  goalsAgainst: number;
  goalDifference: number;
  regulationWins: number;
  overtimeWins: number;
  overtimeLosses: number;
}

export const easternStandings: TeamStanding[] = [
  {
    rank: 1,
    team: "WSH",
    logo: "capitals_logo.png",
    gamesPlayed: 82,
    wins: 51,
    losses: 22,
    ties: 9,
    points: 111,
    goalsFor: 280,
    goalsAgainst: 220,
    goalDifference: 60,
    regulationWins: 40,
    overtimeWins: 11,
    overtimeLosses: 5,
  },
  {
    rank: 2,
    team: "TOR",
    logo: "maple_leafs_logo.png",
    gamesPlayed: 82,
    wins: 52,
    losses: 26,
    ties: 4,
    points: 108,
    goalsFor: 275,
    goalsAgainst: 230,
    goalDifference: 45,
    regulationWins: 41,
    overtimeWins: 11,
    overtimeLosses: 6,
  },
  {
    rank: 3,
    team: "TBL",
    logo: "lightning_logo.png",
    gamesPlayed: 82,
    wins: 47,
    losses: 27,
    ties: 8,
    points: 102,
    goalsFor: 260,
    goalsAgainst: 210,
    goalDifference: 50,
    regulationWins: 36,
    overtimeWins: 11,
    overtimeLosses: 8,
  },
  {
    rank: 4,
    team: "CAR",
    logo: "hurricanes_logo.png",
    gamesPlayed: 82,
    wins: 47,
    losses: 30,
    ties: 5,
    points: 99,
    goalsFor: 250,
    goalsAgainst: 220,
    goalDifference: 30,
    regulationWins: 35,
    overtimeWins: 12,
    overtimeLosses: 7,
  },
  {
    rank: 5,
    team: "FLA",
    logo: "panthers_logo.png",
    gamesPlayed: 82,
    wins: 47,
    losses: 31,
    ties: 4,
    points: 98,
    goalsFor: 240,
    goalsAgainst: 225,
    goalDifference: 15,
    regulationWins: 34,
    overtimeWins: 13,
    overtimeLosses: 8,
  },
  {
    rank: 6,
    team: "OTT",
    logo: "senators_logo.png",
    gamesPlayed: 82,
    wins: 45,
    losses: 30,
    ties: 7,
    points: 97,
    goalsFor: 230,
    goalsAgainst: 215,
    goalDifference: 15,
    regulationWins: 33,
    overtimeWins: 12,
    overtimeLosses: 7,
  },
  {
    rank: 7,
    team: "NJD",
    logo: "devils_logo.png",
    gamesPlayed: 82,
    wins: 42,
    losses: 33,
    ties: 7,
    points: 91,
    goalsFor: 210,
    goalsAgainst: 230,
    goalDifference: -20,
    regulationWins: 30,
    overtimeWins: 12,
    overtimeLosses: 8,
  },
  {
    rank: 8,
    team: "MTL",
    logo: "canadiens_logo.png",
    gamesPlayed: 82,
    wins: 40,
    losses: 31,
    ties: 11,
    points: 91,
    goalsFor: 205,
    goalsAgainst: 215,
    goalDifference: -10,
    regulationWins: 28,
    overtimeWins: 12,
    overtimeLosses: 11,
  },
  {
    rank: 9,
    team: "CBJ",
    logo: "blue_jackets_logo.png",
    gamesPlayed: 82,
    wins: 40,
    losses: 33,
    ties: 9,
    points: 89,
    goalsFor: 200,
    goalsAgainst: 220,
    goalDifference: -20,
    regulationWins: 29,
    overtimeWins: 11,
    overtimeLosses: 9,
  },
  {
    rank: 10,
    team: "DET",
    logo: "red_wings_logo.png",
    gamesPlayed: 82,
    wins: 39,
    losses: 35,
    ties: 8,
    points: 86,
    goalsFor: 195,
    goalsAgainst: 225,
    goalDifference: -30,
    regulationWins: 27,
    overtimeWins: 12,
    overtimeLosses: 10,
  },
  {
    rank: 11,
    team: "NYR",
    logo: "rangers_logo.png",
    gamesPlayed: 82,
    wins: 39,
    losses: 36,
    ties: 7,
    points: 85,
    goalsFor: 190,
    goalsAgainst: 230,
    goalDifference: -40,
    regulationWins: 26,
    overtimeWins: 13,
    overtimeLosses: 10,
  },
  {
    rank: 12,
    team: "NYI",
    logo: "islanders_logo.png",
    gamesPlayed: 82,
    wins: 35,
    losses: 35,
    ties: 12,
    points: 82,
    goalsFor: 180,
    goalsAgainst: 240,
    goalDifference: -60,
    regulationWins: 24,
    overtimeWins: 11,
    overtimeLosses: 12,
  },
];
