import { initializeApp, getApps, getApp } from 'firebase/app';
import { getFirestore, collection, getDocs, addDoc, doc, updateDoc, deleteDoc } from 'firebase/firestore';

// Initialize Firebase (you'll need to add your config)
const firebaseConfig = {
  // apiKey: "AIzaSyAXYi7MCm-aMBeh3bEjs0eJ5eHcGjf9-bw",
  // authDomain: "achieve-cb085.firebaseapp.com",
  // projectId: "achieve-cb085",
  // storageBucket: "achieve-cb085.firebasestorage.app",
  // messagingSenderId: "260668035138",
  // appId: "1:260668035138:web:ba67a28ffd83d01b279ddb",
  // measurementId: "G-JWFXTNTCGZ"
};

const app = getApps().length > 0 ? getApp() : initializeApp(firebaseConfig);
const db = getFirestore(app);

export interface Tournament {
  id: string;
  title: string;
  subtitle: string;
  date: string;
  tags: string[];
  image: string;
  country: string;
}

export interface TournamentDetails extends Tournament {
  location: string;
  participants: number;
  status: string;
  description: string;
}

// Cache for tournaments
let tournamentsCache: TournamentDetails[] = [];

// Helper function to convert Firestore timestamp to date string
const convertFirestoreDate = (firestoreDate: any): string => {
  if (!firestoreDate) return 'Date TBD';
  
  // If it's a Firestore timestamp with seconds and nanoseconds
  if (firestoreDate.seconds && firestoreDate.nanoseconds !== undefined) {
    const date = new Date(firestoreDate.seconds * 1000);
    return date.toLocaleDateString();
  }
  
  // If it's already a Date object
  if (firestoreDate instanceof Date) {
    return firestoreDate.toLocaleDateString();
  }
  
  // If it's a string, return as is
  if (typeof firestoreDate === 'string') {
    return firestoreDate;
  }
  
  return 'Date TBD';
};

// Fetch tournaments from Firebase
export const fetchTournaments = async (): Promise<TournamentDetails[]> => {
  console.log('Fetching tournaments from Firebase...');
  
  try {
    const querySnapshot = await getDocs(collection(db, 'tournaments'));
    const tournaments: TournamentDetails[] = [];
    
    console.log('Firebase query snapshot size:', querySnapshot.size);
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      console.log('Tournament data:', data);
      
      // Use better fallback values for empty fields and handle Firestore timestamps
      const tournament: TournamentDetails = {
        id: doc.id,
        title: data.title || data.name || `Tournament ${doc.id.slice(0, 8)}`,
        subtitle: data.subtitle || data.description || 'Tournament Details',
        date: convertFirestoreDate(data.date || data.startDate),
        tags: Array.isArray(data.tags) ? data.tags : (data.country ? [data.country] : []),
        image: data.image || 'https://picsum.photos/400/300?random=1',
        country: data.country || 'All',
        location: data.location || 'Location TBD',
        participants: data.participants || 0,
        status: data.status || 'Upcoming',
        description: data.description || 'Tournament description coming soon...',
      };
      
      tournaments.push(tournament);
    });
    
    console.log('Processed tournaments:', tournaments.map(t => ({ id: t.id, title: t.title, subtitle: t.subtitle, date: t.date })));
    
    // Update cache with fetched data
    tournamentsCache = tournaments;
    
    return tournaments;
  } catch (error) {
    console.error('Error fetching tournaments:', error);
    // Return default tournaments on error for better UX
    return getDefaultTournaments();
  }
};

// Add default tournaments as fallback
const getDefaultTournaments = (): TournamentDetails[] => {
  return [
    {
      id: "1",
      title: "East West Spring Classic",
      subtitle: "International Youth Tournament",
      date: "28 April – 1 May, 2025",
      tags: ["Hong Kong", "Korea", "U8–U18", "International"],
      image: "https://picsum.photos/400/300?random=1",
      country: "All",
      location: "Hong Kong Ice Hockey Academy",
      participants: 16,
      status: "Registration Open",
      description: "The East West Spring Classic is a premier international youth tournament featuring teams from across Asia.",
    },
    {
      id: "2",
      title: "HKAHC",
      subtitle: "Invitational Amateur Ice Hockey Tournament",
      date: "15 Dec – 21 Feb, 2025",
      tags: ["Hong Kong", "Men's", "Local"],
      image: "https://picsum.photos/400/300?random=2",
      country: "China",
      location: "Mega Ice Arena",
      participants: 12,
      status: "Active",
      description: "The Hong Kong Amateur Hockey Club invitational tournament brings together the best amateur teams from the region.",
    },
  ];
};

// Add tournament to Firebase
export const addTournament = async (tournament: Omit<TournamentDetails, 'id'>): Promise<void> => {
  try {
    await addDoc(collection(db, 'tournaments'), tournament);
    // Refresh cache
    await fetchTournaments();
  } catch (error) {
    console.error('Error adding tournament:', error);
    throw error;
  }
};

// Update tournament in Firebase
export const updateTournament = async (id: string, updates: Partial<TournamentDetails>): Promise<void> => {
  try {
    const tournamentRef = doc(db, 'tournaments', id);
    await updateDoc(tournamentRef, updates);
    // Refresh cache
    await fetchTournaments();
  } catch (error) {
    console.error('Error updating tournament:', error);
    throw error;
  }
};

// Delete tournament from Firebase
export const deleteTournament = async (id: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'tournaments', id));
    // Refresh cache
    await fetchTournaments();
  } catch (error) {
    console.error('Error deleting tournament:', error);
    throw error;
  }
};

// Get tournaments (always fetch from Firebase)
export const getTournaments = async (): Promise<TournamentDetails[]> => {
  return await fetchTournaments();
};

// Export tournaments for backward compatibility
export const tournaments = tournamentsCache;

// Helper function to get tournament by ID
export const getTournamentById = (id: string): TournamentDetails | undefined => {
  return tournamentsCache.find((tournament) => tournament.id === id);
};

export const countries = ["All", "China", "Korea", "Japan"];

export const getStatusColor = (status: string): string => {
  switch (status) {
    case "Active":
      return "#28a745";
    case "Registration Open":
      return "#007bff";
    case "Upcoming":
      return "#ffc107";
    case "Completed":
      return "#6c757d";
    default:
      return "#666";
  }
};
