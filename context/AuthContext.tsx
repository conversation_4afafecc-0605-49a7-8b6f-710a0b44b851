import * as Google from 'expo-auth-session/providers/google';
import * as WebBrowser from 'expo-web-browser';
import {
  createUserWithEmailAndPassword,
  User as FirebaseUser,
  GoogleAuthProvider,
  onAuthStateChanged,
  signInWithCredential,
  signInWithEmailAndPassword,
  signOut,
  updateProfile
} from 'firebase/auth';
import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import { auth } from '../firebase.config';
import type { AuthState, LoginData, SignupData, User } from '../types/auth';

WebBrowser.maybeCompleteAuthSession();

interface AuthContextType extends AuthState {
  login: (data: LoginData) => Promise<void>;
  signup: (data: SignupData) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  loginWithKakao: () => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [kakaoInitialized, setKakaoInitialized] = useState(false);
  const [loginProvider, setLoginProvider] = useState<'email' | 'google' | 'kakao' | null>(null);

  // Fixed Google OAuth configuration
  const [request, response, promptAsync] = Google.useAuthRequest({
    androidClientId: '************-p6tdbnvffp2q4hadev2b8pbnqm4e55hi.apps.googleusercontent.com',
    iosClientId: '************-p6tdbnvffp2q4hadev2b8pbnqm4e55hi.apps.googleusercontent.com',
    webClientId: '************-p6tdbnvffp2q4hadev2b8pbnqm4e55hi.apps.googleusercontent.com',
    scopes: ['openid', 'profile', 'email'],
  });

  const mapFirebaseUser = (firebaseUser: FirebaseUser | null): User | null => {
    if (!firebaseUser) return null;
    return {
      uid: firebaseUser.uid,
      email: firebaseUser.email,
      displayName: firebaseUser.displayName,
      photoURL: firebaseUser.photoURL,
      emailVerified: firebaseUser.emailVerified,
    };
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      console.log('Auth state changed:', firebaseUser ? 'User logged in' : 'User logged out');
      setUser(mapFirebaseUser(firebaseUser));
      setIsLoading(false);
    });
    return unsubscribe;
  }, []);
  useEffect(() => {
    if (response?.type === 'success') {
      const { authentication } = response;
      if (authentication?.accessToken) {
        console.log('🔍 Google auth success, signing in with Firebase...');
        const credential = GoogleAuthProvider.credential(
          authentication.idToken,
          authentication.accessToken
        );
        signInWithCredential(auth, credential)
          .then((userCredential) => {
            console.log('🔍 Firebase Google login successful:', userCredential.user.uid);
            setLoginProvider('google');
          })
          .catch((error) => {
            console.error('🔍 Firebase Google sign in error:', error);
            throw new Error(error.message || 'Google login failed');
          });
      }
    } else if (response?.type === 'error') {
      console.error('🔍 Google auth error:', response.error);
    }
  }, [response]);
  const login = async (data: LoginData) => {
    try {
      console.log('Attempting login with email:', data.email);
      const userCredential = await signInWithEmailAndPassword(auth, data.email, data.password);
      setLoginProvider('email');
      console.log('Login successful:', userCredential.user.uid);
    } catch (error: any) {
      console.error('Login error:', error);
      throw new Error(error.message || 'Login failed');
    }
  };

  const signup = async (data: SignupData) => {
    try {
      if (data.password !== data.confirmPassword) {
        throw new Error('Passwords do not match');
      }
      console.log('Attempting signup with email:', data.email);
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        data.email,
        data.password
      );
      if (data.displayName) {
        await updateProfile(userCredential.user, {
          displayName: data.displayName,
        });
      }
      console.log('Signup successful:', userCredential.user.uid);
    } catch (error: any) {
      console.error('Signup error:', error);
      throw new Error(error.message || 'Signup failed');
    }
  };

  const loginWithGoogle = async () => {
    try {
      console.log('🔍 Starting Google login...');
      
      if (!request) {
        console.error('🔍 Google auth request not ready');
        throw new Error('Google authentication is not ready. Please try again.');
      }
      
      console.log('🔍 Prompting Google auth...');
      const result = await promptAsync();
      
      console.log('🔍 Google auth result:', result);
      
      if (result.type === 'cancel') {
        console.log('🔍 User cancelled Google login');
        return;
      }
      
      if (result.type === 'error') {
        console.error('🔍 Google login error:', result.error);
        throw new Error(result.error?.message || 'Google login failed');
      }
      
      // Success case is handled by the useEffect above
    } catch (error: any) {
      console.error('🔍 Google login error:', error);
      throw new Error(error.message || 'Google login failed');
    }
  };

  // Initialize Kakao SDK for web platform
  useEffect(() => {
    if (Platform.OS === 'web' && !kakaoInitialized) {
      const initializeKakao = async () => {
        try {
          // Load Kakao SDK script
          if (!window.Kakao) {
            const script = document.createElement('script');
            script.src = 'https://t1.kakaocdn.net/kakao_js_sdk/2.7.4/kakao.min.js';
            // Remove the integrity attribute that's causing the issue
            script.crossOrigin = 'anonymous';
            script.async = true;
            
            await new Promise((resolve, reject) => {
              script.onload = resolve;
              script.onerror = reject;
              document.head.appendChild(script);
            });
          }
          
          // Wait a bit for the SDK to be fully loaded
          await new Promise(resolve => setTimeout(resolve, 100));
            // Initialize Kakao SDK
          if (window.Kakao && !window.Kakao.isInitialized()) {
            // For development, we'll skip Kakao initialization to avoid errors
            // Replace with your actual Kakao JavaScript key when you have one
            const KAKAO_JAVASCRIPT_KEY = 'd65bafbd73f5f28f83f9364701cc9461'; // Set to null to disable for now
            
            if (!KAKAO_JAVASCRIPT_KEY) {
              console.warn('⚠️ Kakao JavaScript key not configured - Kakao login will be disabled');
              setKakaoInitialized(false);
              return;
            }
            
            window.Kakao.init(KAKAO_JAVASCRIPT_KEY);
            console.log('🟡 Kakao SDK initialized successfully');
          }
          
          setKakaoInitialized(true);
        } catch (error) {
          console.error('🟡 Failed to initialize Kakao SDK:', error);
          setKakaoInitialized(false);
        }
      };
      
      initializeKakao();
    }
  }, [kakaoInitialized]);  const loginWithKakao = async (): Promise<void> => {
    try {
      console.log('🟡 Starting Kakao login...');
      
      if (Platform.OS === 'web') {
        // Check if Kakao SDK is properly initialized
        if (!window.Kakao) {
          throw new Error('Kakao SDK is not loaded. Please refresh the page and try again.');
        }
        
        if (!kakaoInitialized) {
          throw new Error('Kakao SDK is not initialized. Please wait a moment and try again.');
        }
        
        if (!window.Kakao.isInitialized()) {
          throw new Error('Kakao SDK initialization failed. Please check your Kakao JavaScript key.');
        }

        console.log('🟡 Kakao SDK is ready, starting authorization...');
        console.log('🟡 Debug: Using redirect URI:', 'http://localhost:8081/');

        // Try using Kakao.Auth.login instead of authorize
        return new Promise<void>((resolve, reject) => {
          try {
            window.Kakao.Auth.login({
              success: async (authObj: any) => {
                try {
                  console.log('🟡 Kakao auth success:', authObj);
                  
                  if (!authObj.access_token) {
                    throw new Error('No access token received from Kakao');
                  }
                  
                  // Set access token in Kakao SDK
                  window.Kakao.Auth.setAccessToken(authObj.access_token);
                  
                  // Get user information
                  window.Kakao.API.request({
                    url: '/v2/user/me',
                    success: async (response: any) => {
                      try {
                        console.log('🟡 Kakao user info:', response);
                        
                        if (response.kakao_account?.email) {
                          const tempPassword = `kakao_temp_${Date.now()}`;
                          
                          try {
                            // Try to create a new user
                            const userCredential = await createUserWithEmailAndPassword(
                              auth, 
                              response.kakao_account.email, 
                              tempPassword
                            );
                            
                            // Update profile with Kakao data
                            await updateProfile(userCredential.user, {
                              displayName: response.properties?.nickname || 'Kakao User',
                              photoURL: response.properties?.profile_image || null,
                            });
                            
                            console.log('🟡 Firebase user created with Kakao data:', userCredential.user.uid);
                            setLoginProvider('kakao');
                            resolve();
                            
                          } catch (createError: any) {
                            if (createError.code === 'auth/email-already-in-use') {
                              console.log('🟡 User already exists with this email');
                              throw new Error('This Kakao account email is already registered. Please use regular login.');
                            }
                            throw createError;
                          }
                        } else {
                          throw new Error('No email provided by Kakao. Please enable email permission in Kakao settings.');
                        }
                        
                      } catch (firebaseError) {
                        console.error('🟡 Firebase integration error:', firebaseError);
                        reject(firebaseError);
                      }
                    },
                    fail: (error: any) => {
                      console.error('🟡 Failed to get Kakao user info:', error);
                      reject(new Error('Failed to get user information from Kakao'));
                    }
                  });
                } catch (error) {
                  console.error('🟡 Error in success callback:', error);
                  reject(error);
                }
              },
              fail: (error: any) => {
                console.error('🟡 Kakao login failed:', error);
                reject(new Error(error.error_description || error.error || 'Kakao login failed'));
              }
            });
          } catch (sdkError) {
            console.error('🟡 SDK call error:', sdkError);
            reject(sdkError);
          }
        });
      }

      // For mobile platforms, use WebBrowser to open Kakao OAuth
      const redirectUri = 'https://auth.expo.io/@anonymous/hockey-app';
      const KAKAO_REST_API_KEY = '683d162508f13894c97fb4d09f48b38d'; // Use REST API key for mobile
      
      // if (KAKAO_REST_API_KEY === 'YOUR_KAKAO_REST_API_KEY') {
      //   throw new Error('Kakao REST API key not configured for mobile platforms');
      // }
      
      const kakaoAuthUrl = `https://kauth.kakao.com/oauth/authorize?client_id=${KAKAO_REST_API_KEY}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code`;
      
      const result = await WebBrowser.openAuthSessionAsync(kakaoAuthUrl, redirectUri);
      
      if (result.type === 'success' && result.url) {
        const url = new URL(result.url);
        const code = url.searchParams.get('code');
        
        if (code) {
          // Exchange code for access token
          const tokenResponse = await fetch('https://kauth.kakao.com/oauth/token', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
              grant_type: 'authorization_code',
              client_id: KAKAO_REST_API_KEY,
              redirect_uri: redirectUri,
              code: code,
            }),
          });
          
          const tokenData = await tokenResponse.json();
          
          if (tokenData.access_token) {
            // Get user profile
            const profileResponse = await fetch('https://kapi.kakao.com/v2/user/me', {
              headers: {
                'Authorization': `Bearer ${tokenData.access_token}`,
              },
            });
            
            const profile = await profileResponse.json();
            console.log('🟡 Kakao profile:', profile);
            
            // For mobile, implement the same fallback as web
            if (profile.kakao_account?.email) {
              const tempPassword = `kakao_temp_${Date.now()}`;
              
              try {
                const userCredential = await createUserWithEmailAndPassword(auth, profile.kakao_account.email, tempPassword);
                
                await updateProfile(userCredential.user, {
                  displayName: profile.properties?.nickname || 'Kakao User',
                  photoURL: profile.properties?.profile_image || null,
                });
                
                console.log('🟡 Mobile Firebase user created with Kakao data:', userCredential.user.uid);
                
              } catch (createError: any) {
                if (createError.code === 'auth/email-already-in-use') {
                  throw new Error('This Kakao account email is already registered. Please use regular login or implement proper Kakao backend verification.');
                }
                throw createError;
              }
            } else {
              throw new Error('No email provided by Kakao. Email permission is required.');
            }
          } else {
            throw new Error('Failed to get Kakao access token');
          }
        } else {
          throw new Error('No authorization code received from Kakao');
        }
      } else if (result.type === 'cancel') {
        throw new Error('Kakao login was cancelled by user');
      } else {
        throw new Error('Kakao login failed');
      }

    } catch (error: any) {
      console.error('🟡 Kakao login error:', error);
      
      // Handle specific errors
      if (error.message?.includes('cancelled') || error.message?.includes('canceled')) {
        throw new Error('Kakao login was cancelled by user');
      } else {
        throw new Error(error.message || 'Kakao login failed');
      }
    }
  };  const logout = async () => {
    try {
      console.log('🔓 AuthContext: Starting logout process...');
      console.log('🔓 AuthContext: Current user before logout:', user?.email);
      console.log('🔓 AuthContext: Login provider:', loginProvider);
      
      // Immediately clear local state to trigger re-render
      setUser(null);
      setLoginProvider(null);
      console.log('🔓 AuthContext: User state cleared immediately');
      
      // Only logout from Kakao if user actually used Kakao to login
      if (loginProvider === 'kakao' && Platform.OS === 'web') {
        try {
          // Check if Kakao SDK is available and initialized
          if (window.Kakao && window.Kakao.isInitialized && window.Kakao.isInitialized()) {
            // Check if there's an active Kakao session
            const accessToken = window.Kakao.Auth?.getAccessToken?.();
            if (accessToken) {
              console.log('🟡 Active Kakao session found, logging out...');
              await new Promise((resolve) => {
                window.Kakao.Auth.logout({
                  success: () => {
                    console.log('🟡 Kakao logout completed');
                    resolve(void 0);
                  },
                  fail: (error: any) => {
                    console.log('🟡 Kakao logout failed:', error);
                    resolve(void 0); // Don't fail the entire logout process
                  }
                });
              });
            } else {
              console.log('🟡 No active Kakao session, skipping Kakao logout');
            }
          } else {
            console.log('🟡 Kakao SDK not available or not initialized, skipping Kakao logout');
          }
        } catch (kakaoError) {
          console.log('🟡 Kakao logout error (non-blocking):', kakaoError);
          // Don't throw - continue with Firebase logout
        }
      } else {
        console.log('🔓 AuthContext: Skipping Kakao logout (provider: ' + loginProvider + ')');
      }
      
      // Always sign out from Firebase - this is the critical part
      console.log('🔓 AuthContext: Attempting Firebase signOut...');
      await signOut(auth);
      console.log('🔓 AuthContext: Firebase signOut completed successfully');
      
    } catch (error: any) {
      console.error('❌ AuthContext: Logout error:', error);
      
      // Force clear the user state even if Firebase logout fails
      setUser(null);
      setLoginProvider(null);
      console.log('🔓 AuthContext: Force cleared user state due to error');
      
      // Don't throw error - we want logout to always succeed from UI perspective
      console.log('🔓 AuthContext: Logout completed (with errors)');
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    signup,
    loginWithGoogle,
    loginWithKakao,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Add type declaration for Kakao SDK
declare global {
  interface Window {
    Kakao: any;
  }
}
