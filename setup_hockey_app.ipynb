{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Hockey App Setup Guide\n", "\n", "This notebook will guide you (or your teammates) through setting up and running the Hockey App React Native/Expo project using the provided `package.json`.\n", "\n", "> **Note:** All commands should be run in your terminal (PowerShell, CMD, or bash), not inside this notebook."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON><PERSON> the Repository\n", "\n", "Replace `<your-repo-url>` with your actual repository URL.\n", "\n", "(*Double click* the code below to copy and paste it)  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["```sh\n", "git clone <your-repo-url>\n", "cd hockey-app"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Install Node.js (if not already installed)\n", "\n", "- Download and install Node.js from [https://nodejs.org/](https://nodejs.org/).\n", "- This will also install `npm` (Node Package Manager)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Install Project Dependencies\n", "\n", "Run this command in your project folder (`hockey-app`):\n", "\n", "(*Double click* the code below to copy and paste it) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["```sh\n", "npm install"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Start the Expo Project\n", "\n", "You can start the project for development using Expo. Run one of the following commands:\n", "\n", "(*Double click* the code below to copy and paste it) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["```sh\n", "npm start"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Run on Web, Android, or iOS\n", "\n", "- To run on web:\n", "  ```sh\n", "  npm run web\n", "  ```\n", "- To run on Android:\n", "  ```sh\n", "  npm run android\n", "  ```\n", "- To run on iOS (Mac only):\n", "  ```sh\n", "  npm run ios\n", "  ```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON>shooting\n", "\n", "- If you see errors about missing packages, make sure you are in the correct folder and have run `npm install`.\n", "- If you change dependencies, run `npm install` again.\n", "- For Expo-specific issues, see [Expo Documentation](https://docs.expo.dev/)."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": ""}}, "nbformat": 4, "nbformat_minor": 2}